<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'CATAT')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php
        $useBuiltAssets = app()->environment('production') ||
                         str_contains(config('app.url'), 'ngrok') ||
                         str_contains(request()->getHost(), 'ngrok') ||
                         file_exists(public_path('build/manifest.json'));
    ?>

    <?php if($useBuiltAssets && file_exists(public_path('build/manifest.json'))): ?>
        <!-- Production/ngrok: Use built assets -->
        <?php
            $manifest = json_decode(file_get_contents(public_path('build/manifest.json')), true);
            $cssFile = $manifest['resources/css/app.css']['file'] ?? 'assets/app-DQhsI7HA.css';
            $jsFile = $manifest['resources/js/app.js']['file'] ?? 'assets/app-CQXzpyVp.js';
        ?>
        <link rel="stylesheet" href="<?php echo e(asset('build/' . $cssFile)); ?>">
        <script src="<?php echo e(asset('build/' . $jsFile)); ?>" defer></script>
    <?php else: ?>
        <!-- Development: Use Vite -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php endif; ?>
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <?php echo $__env->yieldContent('content'); ?>
    </div>
</body>
</html> <?php /**PATH C:\Users\<USER>\Documents\KULIAH\tugas_akhir\resources\views/layouts/auth.blade.php ENDPATH**/ ?>