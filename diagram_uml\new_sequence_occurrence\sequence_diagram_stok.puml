@startuml sequence_diagram_stok
!theme plain
title Sequence Diagram - Manaj<PERSON><PERSON>ok (Simplified)

' Styling sesuai referensi
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowColor #333333
skinparam sequenceActorBorderColor #333333
skinparam sequenceActorBackgroundColor #E3F2FD
skinparam sequenceParticipantBorderColor #333333
skinparam sequenceParticipantBackgroundColor #F3E5F5
skinparam sequenceGroupBackgroundColor #F8F9FA
skinparam sequenceLifeLineBorderColor #6C757D
skinparam sequenceLifeLineBackgroundColor #FFFFFF

' Actors dan Participants (1 actor + 3 objects)
actor "👨‍💼 Manajer" as Manajer
participant "🖥️ UI" as UI
participant "🌐 Controller" as Controller
participant "🗄️ Database" as Database

' Sequence Flow
Manajer -> UI: 1. A<PERSON><PERSON>du<PERSON>
activate UI
UI -> Controller: :order(GET /products)
activate Controller
Controller -> Database: :order(SELECT products, stock)
activate Database
Database --> Controller: :order(product data)
deactivate Database
Controller --> UI: :order(product list)
deactivate Controller
UI --> Manajer: :order(tampilkan daftar produk)
deactivate UI

Manajer -> UI: 2. <PERSON><PERSON><PERSON>duk (Create/Edit)
activate UI
UI -> Controller: :order(POST /products/store)
activate Controller
Controller -> Database: :order(INSERT/UPDATE products)
activate Database
Database --> Controller: :order(product saved)
deactivate Database
Controller --> UI: :order(redirect to products)
deactivate Controller
UI --> Manajer: :order(tampilkan konfirmasi)
deactivate UI

Manajer -> UI: 3. Update Stok
activate UI
UI -> Controller: :order(POST /products/{id}/stock)
activate Controller
Controller -> Database: :order(UPDATE stock, INSERT stock_history)
activate Database
Database --> Controller: :order(stock updated)
deactivate Database
Controller --> UI: :order(updated stock info)
deactivate Controller
UI --> Manajer: :order(tampilkan stok terbaru)
deactivate UI

note right of Manajer
  **Implementasi Laravel:**
  - ProductController
  - Product Model
  - Category Model
  - Blade Templates
end note

@enduml
