@startuml Sequence Diagram - <PERSON><PERSON><PERSON> Pengguna

title Sequence Diagram - <PERSON><PERSON><PERSON> (Sistem POS UMKM)

actor "<PERSON>rek<PERSON>/Manajer" as DM
participant "UI (Web Interface)" as UI
participant "UserController" as UC
participant "User Model" as UM
participant "UserLog Model" as UL
participant "Database" as DB

== Aks<PERSON> Kelola Pengguna ==
DM -> UI : :1(aks<PERSON> halaman kelola pengguna)
activate UI
UI -> UC : :2(index)
activate UC
UC -> UM : :3(getAllUsers)
activate UM
UM -> DB : :4(SELECT * FROM users)
activate DB
DB --> UM : :5(data users)
deactivate DB
UM --> UC : :6(collection users)
deactivate UM
UC --> UI : :7(view dengan data users)
deactivate UC
UI --> DM : :8(tampilkan halaman kelola pengguna)
deactivate UI

== Pilih Aksi - Tambah Akun ==
DM -> UI : :9(pilih tambah akun)
activate UI
UI -> UC : :10(create)
activate UC
UC --> UI : :11(form tambah user)
deactivate UC
UI --> DM : :12(tampilkan form tambah)
deactivate UI

DM -> UI : :13(isi data user baru)
activate UI
UI -> UC : :14(store, data)
activate UC
UC -> UM : :15(validateProfileData, data)
activate UM
UM --> UC : :16(validation result)
deactivate UM

alt validation success
    UC -> UM : :17(createUser, data)
    activate UM
    UM -> DB : :18(INSERT INTO users)
    activate DB
    DB --> UM : :19(user created)
    deactivate DB
    UM --> UC : :20(new user object)
    deactivate UM
    
    UC -> UL : :21(logUserAction, 'create', userId, createdBy)
    activate UL
    UL -> DB : :22(INSERT INTO user_logs)
    activate DB
    DB --> UL : :23(log created)
    deactivate DB
    UL --> UC : :24()
    deactivate UL
    
    UC --> UI : :25(redirect dengan success message)
    deactivate UC
    UI --> DM : :26(tampilkan success message)
    deactivate UI
else validation failed
    UC --> UI : :27(redirect dengan error message)
    deactivate UC
    UI --> DM : :28(tampilkan error message)
    deactivate UI
end

== Pilih Aksi - Edit Akun ==
DM -> UI : :29(pilih edit akun)
activate UI
UI -> UC : :30(edit, userId)
activate UC
UC -> UM : :31(getUserById, userId)
activate UM
UM -> DB : :32(SELECT * FROM users WHERE id)
activate DB
DB --> UM : :33(user data)
deactivate DB
UM --> UC : :34(user object)
deactivate UM
UC --> UI : :35(form edit dengan data user)
deactivate UC
UI --> DM : :36(tampilkan form edit)
deactivate UI

DM -> UI : :37(update data user)
activate UI
UI -> UC : :38(update, userId, data)
activate UC
UC -> UM : :39(validateProfileData, data)
activate UM
UM --> UC : :40(validation result)
deactivate UM

alt validation success
    UC -> UM : :41(updateUser, userId, data)
    activate UM
    UM -> DB : :42(UPDATE users SET ... WHERE id)
    activate DB
    DB --> UM : :43(user updated)
    deactivate DB
    UM --> UC : :44(updated user object)
    deactivate UM
    
    UC -> UL : :45(logUserAction, 'update', userId, updatedBy)
    activate UL
    UL -> DB : :46(INSERT INTO user_logs)
    activate DB
    DB --> UL : :47(log created)
    deactivate DB
    UL --> UC : :48()
    deactivate UL
    
    UC --> UI : :49(redirect dengan success message)
    deactivate UC
    UI --> DM : :50(tampilkan success message)
    deactivate UI
else validation failed
    UC --> UI : :51(redirect dengan error message)
    deactivate UC
    UI --> DM : :52(tampilkan error message)
    deactivate UI
end

== Pilih Aksi - Hapus Akun ==
DM -> UI : :53(pilih hapus akun)
activate UI
UI -> UC : :54(destroy, userId)
activate UC
UC -> UM : :55(canManageUser, userId)
activate UM
UM --> UC : :56(permission check result)
deactivate UM

alt permission granted
    UC -> UM : :57(deleteUser, userId)
    activate UM
    UM -> DB : :58(UPDATE users SET deleted_at WHERE id)
    activate DB
    DB --> UM : :59(user soft deleted)
    deactivate DB
    UM --> UC : :60(deletion result)
    deactivate UM
    
    UC -> UL : :61(logUserAction, 'delete', userId, deletedBy)
    activate UL
    UL -> DB : :62(INSERT INTO user_logs)
    activate DB
    DB --> UL : :63(log created)
    deactivate DB
    UL --> UC : :64()
    deactivate UL
    
    UC --> UI : :65(redirect dengan success message)
    deactivate UC
    UI --> DM : :66(tampilkan success message)
    deactivate UI
else permission denied
    UC --> UI : :67(redirect dengan error message)
    deactivate UC
    UI --> DM : :68(tampilkan error message)
    deactivate UI
end

@enduml
