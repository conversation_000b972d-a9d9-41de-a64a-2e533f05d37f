@startuml sequence_diagram_retur
!theme plain
title Sequence Diagram - Proses Retur (Simplified)

' Styling sesuai referensi
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowColor #333333
skinparam sequenceActorBorderColor #333333
skinparam sequenceActorBackgroundColor #E3F2FD
skinparam sequenceParticipantBorderColor #333333
skinparam sequenceParticipantBackgroundColor #F3E5F5
skinparam sequenceGroupBackgroundColor #F8F9FA
skinparam sequenceLifeLineBorderColor #6C757D
skinparam sequenceLifeLineBackgroundColor #FFFFFF

' Actors dan Participants (1 actor + 3 objects)
actor "👨‍💼 Manajer" as Manajer
participant "🖥️ UI" as UI
participant "🌐 Controller" as Controller
participant "🗄️ Database" as Database

' Sequence Flow
Manajer -> UI: 1. Aks<PERSON>u <PERSON>
activate UI
UI -> Controller: :order(GET /returns)
activate Controller
Controller -> Database: :order(SELECT return_requests)
activate Database
Database --> Controller: :order(return data)
deactivate Database
Controller --> UI: :order(return list)
deactivate Controller
UI --> Manajer: :order(tampilkan daftar retur)
deactivate UI

Manajer -> UI: 2. Review Permin<PERSON><PERSON>tur
activate UI
UI -> Controller: :order(GET /returns/{id})
activate Controller
Controller -> Database: :order(SELECT return_details)
activate Database
Database --> Controller: :order(return details)
deactivate Database
Controller --> UI: :order(return form)
deactivate Controller
UI --> Manajer: :order(tampilkan detail retur)
deactivate UI

Manajer -> UI: 3. Keputusan Retur (Setuju/Tolak)
activate UI
UI -> Controller: :order(POST /returns/{id}/approve)
activate Controller
Controller -> Database: :order(UPDATE return_status, UPDATE stock)
activate Database
Database --> Controller: :order(return processed)
deactivate Database
Controller --> UI: :order(redirect to returns)
deactivate Controller
UI --> Manajer: :order(tampilkan konfirmasi)
deactivate UI

note right of Manajer
  **Implementasi Laravel:**
  - ReturnController
  - ReturnTransaction Model
  - Product Model
  - Blade Templates
end note

@enduml
