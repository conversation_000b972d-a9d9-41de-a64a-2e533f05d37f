<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CATAT">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <title><?php echo $__env->yieldContent('title', 'CATAT'); ?></title>

    <!-- Flash Messages for Toast -->
    <?php if(session('success')): ?>
        <meta name="flash-success" content="<?php echo e(session('success')); ?>">
    <?php endif; ?>
    <?php if(session('error')): ?>
        <meta name="flash-error" content="<?php echo e(session('error')); ?>">
    <?php endif; ?>
    <?php if(session('warning')): ?>
        <meta name="flash-warning" content="<?php echo e(session('warning')); ?>">
    <?php endif; ?>
    <?php if(session('info')): ?>
        <meta name="flash-info" content="<?php echo e(session('info')); ?>">
    <?php endif; ?>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <?php
        $useBuiltAssets = app()->environment('production') ||
                         str_contains(config('app.url'), 'ngrok') ||
                         str_contains(request()->getHost(), 'ngrok') ||
                         file_exists(public_path('build/manifest.json'));
    ?>

    <?php if($useBuiltAssets && file_exists(public_path('build/manifest.json'))): ?>
        <!-- Production/ngrok: Use built assets -->
        <?php
            $manifest = json_decode(file_get_contents(public_path('build/manifest.json')), true);
            $cssFile = $manifest['resources/css/app.css']['file'] ?? 'assets/app-DQhsI7HA.css';
            $jsFile = $manifest['resources/js/app.js']['file'] ?? 'assets/app-CQXzpyVp.js';
        ?>
        <link rel="stylesheet" href="<?php echo e(asset('build/' . $cssFile)); ?>">
        <script src="<?php echo e(asset('build/' . $jsFile)); ?>" defer></script>
    <?php else: ?>
        <!-- Development: Use Vite -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php endif; ?>
    <script src="<?php echo e(asset('js/toast.js')); ?>"></script>
    <script src="<?php echo e(asset('js/mobile.js')); ?>"></script>

    <!-- Mobile Responsive CSS -->
    <style>
        /* Mobile Header Base Styles */
        .mobile-header {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100vw;
            max-width: 100vw;
            height: 50px;
            z-index: 1001;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            color: white;
        }

        .mobile-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        /* Mobile Layout Fixes */
        @media (max-width: 768px) {
            html, body {
                overflow-x: hidden !important;
                width: 100vw !important;
                max-width: 100vw !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .main-content {
                width: 100% !important;
                max-width: 100vw !important;
                margin-left: 0 !important;
                padding: 60px 10px 10px 10px !important;
                box-sizing: border-box !important;
                overflow-x: hidden !important;
            }

            .sidebar {
                transform: translateX(-100%) !important;
                z-index: 1000 !important;
                width: 280px !important;
                position: fixed !important;
            }

            .sidebar.active, .sidebar.open {
                transform: translateX(0) !important;
            }

            /* Ensure all content respects viewport */
            * {
                max-width: 100vw !important;
                box-sizing: border-box !important;
            }

            .mobile-header {
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100vw !important;
                max-width: 100vw !important;
                height: 50px !important;
                z-index: 1001 !important;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
                padding: 10px 15px !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
                align-items: center !important;
                justify-content: space-between !important;
                box-sizing: border-box !important;
                color: white !important;
            }

            .mobile-title {
                font-size: 18px !important;
                font-weight: 600 !important;
                margin: 0 !important;
            }

            .mobile-menu-btn {
                background: none !important;
                border: none !important;
                color: white !important;
                font-size: 18px !important;
                padding: 8px !important;
                cursor: pointer !important;
                border-radius: 4px !important;
                transition: background-color 0.2s !important;
            }

            .mobile-menu-btn:hover {
                background: rgba(255, 255, 255, 0.1) !important;
            }

            .content-wrapper {
                margin-top: 60px !important;
                padding: 15px 10px !important;
            }

            .dashboard-cards {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
                padding: 0 !important;
            }

            .card {
                margin: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            }

            .card-content {
                padding: 15px !important;
            }

            .card-icon {
                width: 50px !important;
                height: 50px !important;
                font-size: 24px !important;
            }

            .card-info h3 {
                font-size: 18px !important;
                margin-bottom: 5px !important;
            }

            .card-info p {
                font-size: 12px !important;
            }

            .card-value {
                font-size: 20px !important;
            }

            /* Fix untuk container yang overflow */
            * {
                max-width: 100vw !important;
                box-sizing: border-box !important;
            }

            .container, .row, .col {
                width: 100% !important;
                max-width: 100% !important;
                padding-left: 10px !important;
                padding-right: 10px !important;
            }
        }

        /* Mobile Header */
        .mobile-header {
            display: none;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .mobile-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        .mobile-overlay.active {
            display: block;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">CATAT</h1>
        <div style="width: 32px;"></div> <!-- Spacer for centering -->
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="<?php echo e(route('login')); ?>" class="logo">
                <img src="<?php echo e(asset('logo.png')); ?>" alt="CATAT Logo" style="height: 160px; width: auto;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none; color: white; font-size: 24px; font-weight: 700; text-align: center; padding: 20px;">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                    CATAT
                </div>
            </a>

            <!-- Welcome Message -->
            <?php if(auth()->check()): ?>
            <div class="welcome-message">
                <div style="color: white; font-size: 13px; font-weight: 600; text-shadow: 0 1px 2px rgb(171, 161, 161); line-height: 1.4; text-align: center;">
                    Selamat Datang<br>
                    <span style="font-size: 15px; font-weight: 700;"><?php echo e(Auth::user()->name); ?></span><br>
                    <span style="font-size: 11px; opacity: 0.9;">
                        Di Website
                        <?php if(Auth::user()->role === 'director'): ?>
                            Direktur
                        <?php elseif(Auth::user()->role === 'manager'): ?>
                            Manajer
                        <?php elseif(Auth::user()->role === 'employee'): ?>
                            Pegawai
                        <?php else: ?>
                            <?php echo e(ucfirst(Auth::user()->role)); ?>

                        <?php endif; ?>
                    </span>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <ul class="nav-list">
            <?php if(auth()->check() && auth()->user()->isDirector()): ?>
                <a href="<?php echo e(route('director.dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('director.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="<?php echo e(route('director.branches.index')); ?>" class="nav-item <?php echo e(request()->routeIs('director.branches.*') ? 'active' : ''); ?>">
                    <i class="fas fa-store"></i>
                    <span class="nav-text">Manajemen Cabang</span>
                </a>
                <a href="<?php echo e(route('director.users.index')); ?>" class="nav-item <?php echo e(request()->routeIs('director.users.*') ? 'active' : ''); ?>">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="<?php echo e(route('director.reports.integrated')); ?>" class="nav-item <?php echo e(request()->routeIs('director.reports.integrated') ? 'active' : ''); ?>">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            <?php elseif(auth()->check() && auth()->user()->isManager()): ?>
                <a href="<?php echo e(route('manager.dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="<?php echo e(route('manager.products.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.products.*') ? 'active' : ''); ?>">
                    <i class="fas fa-box"></i>
                    <span class="nav-text">Manajemen Produk</span>
                </a>
                <a href="<?php echo e(route('manager.categories.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.categories.*') ? 'active' : ''); ?>">
                    <i class="fas fa-tags"></i>
                    <span class="nav-text">Manajemen Kategori</span>
                </a>
                <a href="<?php echo e(route('manager.employees.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.employees.*') ? 'active' : ''); ?>">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="<?php echo e(route('manager.returns.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.returns.*') ? 'active' : ''); ?>">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Persetujuan Retur</span>
                </a>
                <a href="<?php echo e(route('manager.damaged-stock.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.damaged-stock.*') ? 'active' : ''); ?>">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="nav-text">Barang Rusak</span>
                </a>
                <a href="<?php echo e(route('manager.reports.integrated')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.reports.integrated') ? 'active' : ''); ?>">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            <?php elseif(auth()->check() && auth()->user()->isEmployee()): ?>
                <a href="<?php echo e(route('employee.dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="<?php echo e(route('employee.transactions.create')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.transactions.*') ? 'active' : ''); ?>">
                    <i class="fas fa-cash-register"></i>
                    <span class="nav-text">Transaksi Penjualan</span>
                </a>
                <a href="<?php echo e(route('employee.returns.index')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.returns.*') ? 'active' : ''); ?>">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Retur Barang</span>
                </a>
                <a href="<?php echo e(route('employee.stocks.index')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.stocks.*') ? 'active' : ''); ?>">
                    <i class="fas fa-boxes"></i>
                    <span class="nav-text">Stok Produk</span>
                </a>
            <?php endif; ?>

            <a href="<?php echo e(route('logout')); ?>" class="nav-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Keluar</span>
            </a>
            <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                <?php echo csrf_field(); ?>
            </form>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-wrapper">
            
            
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </main>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    
    <script>
        // Mobile Menu Functions
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (sidebar.classList.contains('active') || sidebar.classList.contains('open')) {
                sidebar.classList.remove('active', 'open');
                overlay.classList.remove('active');
                if (menuBtn) menuBtn.className = 'fas fa-bars';
            } else {
                sidebar.classList.add('active', 'open');
                overlay.classList.add('active');
                if (menuBtn) menuBtn.className = 'fas fa-times';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            sidebar.classList.remove('active', 'open');
            overlay.classList.remove('active');
            if (menuBtn) menuBtn.className = 'fas fa-bars';
        }

        // Close menu when clicking nav item
        document.addEventListener('DOMContentLoaded', function() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', closeMobileMenu);
            });

            // Prevent horizontal scroll
            document.body.style.overflowX = 'hidden';
            document.documentElement.style.overflowX = 'hidden';

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                const sidebar = document.querySelector('.sidebar');
                const menuBtn = document.querySelector('.mobile-menu-btn');

                if (window.innerWidth <= 768 &&
                    sidebar && menuBtn &&
                    !sidebar.contains(e.target) &&
                    !menuBtn.contains(e.target) &&
                    (sidebar.classList.contains('active') || sidebar.classList.contains('open'))) {
                    closeMobileMenu();
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    closeMobileMenu();
                }
            });

            // Show flash messages as toasts
            <?php if(session('success')): ?>
                showToast('<?php echo e(session('success')); ?>', 'success');
            <?php endif; ?>

            <?php if(session('error')): ?>
                showToast('<?php echo e(session('error')); ?>', 'error');
            <?php endif; ?>

            <?php if(session('warning')): ?>
                showToast('<?php echo e(session('warning')); ?>', 'warning');
            <?php endif; ?>

            <?php if(session('info')): ?>
                showToast('<?php echo e(session('info')); ?>', 'info');
            <?php endif; ?>
        });

    </script>
    <script>
        // Auto-refresh CSRF token every 30 minutes to prevent expiration
        setInterval(function() {
            fetch('/csrf-token')
                .then(response => response.json())
                .then(data => {
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.token);
                    }
                    // Update all CSRF token inputs
                    document.querySelectorAll('input[name="_token"]').forEach(input => {
                        input.value = data.token;
                    });
                })
                .catch(error => {
                    console.log('CSRF token refresh failed:', error);
                });
        }, 30 * 60 * 1000); // 30 minutes

        // Handle logout form submission with better error handling
        document.addEventListener('DOMContentLoaded', function() {
            const logoutForm = document.getElementById('logout-form');
            if (logoutForm) {
                logoutForm.addEventListener('submit', function(e) {
                    // If CSRF token is missing or expired, redirect to login
                    const csrfTokenInput = logoutForm.querySelector('input[name="_token"]');
                    if (!csrfTokenInput || !csrfTokenInput.value) {
                        e.preventDefault();
                        window.location.href = '<?php echo e(route("login")); ?>';
                        return false;
                    }
                });
            }

            // Handle logout link click with fallback
            const logoutLink = document.querySelector('a[href="<?php echo e(route("logout")); ?>"]');
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = document.getElementById('logout-form');
                    if (form) {
                        form.submit();
                    } else {
                        // Fallback: redirect to logout GET route
                        window.location.href = '<?php echo e(route("logout.get")); ?>';
                    }
                });
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\KULIAH\tugas_akhir\resources\views/layouts/app.blade.php ENDPATH**/ ?>