﻿Scenario ID;Case ID;Test Scenario;Type ;Test Case ;Pre Condition ;Steps Description ;Feedback;;
TS.001;TC.001.001;<PERSON><PERSON><PERSON><PERSON>gguna ;Positif ;Input username yang valid ;Mengakses website POG ;1. Mengakses website POG ;Proses  login sudah berjalan dengan baik ;;
;;;;;;2. Memasukan username dan password ;;;
;;;;;;3. <PERSON><PERSON> ;;;
TS.001;TC.001.002;<PERSON><PERSON><PERSON><PERSON>;Negatif;Input username yang tidak valid ;Mengakses website POG ;1. Mengakses website POG ;Jika kita salah menginputkan dari sistem langsung otomatis memberitahukan bahwa data yang dimasukan tidak valid dan diminta untuk memasukan ulang ;;
;;;;;;2. Memasukan username yang tidak valid dan password ;;;
;;;;;;3. <PERSON><PERSON> Ma<PERSON> ;;;
TS.001;TC.001.003;<PERSON><PERSON><PERSON><PERSON> ;Positif ;Input password yang valid ;Mengakses website POG ;1. Mengakses website POG ;Proses  login sudah berjalan dengan baik ;;
;;;;;;2. Memasukan username dan password ;;;
;;;;;;3. Tekan Tombol Masuk ;;;
TS.001;TC.001.004;Pengujian Login Pengguna;Negatif;Input password yang tidak valid ;Mengakses website POG ;1. Mengakses website POG ;Jika kita salah menginputkan dari sistem langsung otomatis memberitahukan bahwa data yang dimasukan tidak valid dan diminta untuk memasukan ulang ;;
;;;;;;2. Memasukan username dan password yang tidak valid;;;
;;;;;;3. Tekan Tombol Masuk ;;;
TS.002;TC.002.001;Pengujian Pengisian Jumlah Pesanan;Negatif ;Input jumlah pesanan lebih dari jumlah stok;Mengakses halaman detail produk;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa pesan melebihi dari jumlah stok;;
;;;;;;2. Memilih produk yang ingin dipesan ;;;
;;;;;;3. Masukan jumlah pesanan yang ingin dipesan;;;
;;;;;;4. Tekan tombol tambahkan ke keranjang ;;;
TS.003;TC.003.001;Pengujian Pengisian Kolom Nomor Telepon ;Negatif ;Input kolom nomor telepon dengan huruf ;Mengakses halaman checkout ;1. Mengakses website POG ;Untuk kolom nomor telepon mungkin pengisian dapat dibatasi dengan angka saja;;
;;;;;;2. Mengakses halaman checkout ;;;
;;;;;;3. Memasukan informasi tentang pengeriman ;;;
;;;;;;4. Tekan tombol checkout ;;;
TS.003;TC.003.002;Pengujian Pengisian Kolom Nomor Telepon ;Negatif ;Input kolom nomor telepon dengan lebih dari  12 angka ;Mengakses halaman checkout ;1. Mengakses website POG ;Untuk kolom nomor telepon bisa dibatasi sesuai dengan jumlah nomor telepon yang normal yaitu 12 angka;;
;;;;;;2. Mengakses halaman checkout ;;;
;;;;;;3. Memasukan informasi tentang pengeriman ;;;
;;;;;;4. Tekan tombol checkout ;;;
TS.004;TC.004.001;Pengujian Pengisian Kolom Stok ;Negatif ;Input kolom stok dengan 30 angka;Mengkases halaman tambah produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan stok dengan jumlah yang tidak wajar;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman tambah produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.004;TC.004.002;Pengujian Pengisian Kolom Harga ;Negatif ;Input kolom harga dengan 30 angka ;Mengkases halaman tambah produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan harga dengan jumlah yang tidak wajar;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman tambah produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.004;TC.004.003;Pengujian Pengisian Kolom Harga ;Negatif ;Input kolom harga dengan huruf ;Mengkases halaman tambah produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan harga format huruf melainkan hanya angka saja;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman tambah produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.004;TC.004.004;Pengujian Pengisian Kolom Stok ;Negatif ;Input kolom stok dengan huruf;Mengkases halaman tambah produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan kolom stok dengan huruf melainkan hanya angka saja ;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman tambah produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.004;TC.004.005;Pengujian Kolom Penambahan Gambar Produk ;Positif ;Input kolom gambar produk sesuai dengan ketentuan ;Mengkases halaman tambah produk ;1. Mengakses website POG ;Mungkin kedepannya kami sebagai admin dapat menambahkan gambar produk kami;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman tambah produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.005;TC.005.001;Pengujian Pengisian Kolom Stok ;Negatif ;Input kolom stok dengan 30 angka;Mengkases halaman edit produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan stok dengan jumlah yang tidak wajar;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.005;TC.005.002;Pengujian Pengisian Kolom Harga ;Negatif ;Input kolom harga dengan 30 angka ;Mengkases halaman edit produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan harga dengan jumlah yang tidak wajar;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.005;TC.005.003;Pengujian Pengisian Kolom Harga ;Negatif ;Input kolom harga dengan huruf ;Mengkases halaman edit produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan harga format huruf melainkan hanya angka saja;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.005;TC.005.004;Pengujian Pengisian Kolom Stok ;Negatif ;Input kolom stok dengan huruf;Mengkases halaman edit produk ;1. Mengakses website POG ;Sudah cukup baik, sistem langsung otomatis memberitahukan jika tidak bisa menambahkan kolom stok dengan huruf melainkan hanya angka saja ;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.005;TC.005.005;Pengujian Kolom Penambahan Gambar Produk ;Positif ;Input kolom gambar produk sesuai dengan ketentuan ;Mengkases halaman edit produk ;1. Mengakses website POG ;Mungkin kedepannya kami sebagai admin dapat menambahkan gambar produk kami;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit produk ;;;
;;;;;;4. Memasukan informasi tentang produk;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.006;TC.006.001;Pengujian Pengisian Kolom Nomor Telepon ;Negatif ;Input kolom nomor telepon dengan huruf ;Mengakses halaman edit informasi toko;1. Mengakses website POG ;Untuk kolom nomor telepon mungkin pengisian dapat dibatasi dengan angka saja;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit informasi toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Simpan Perubahan ;;;
TS.006;TC.006.002;Pengujian Kolom Penambahan Gambar Logo Toko;Positif ;Input kolom logo gambar toko sesuai dengan ketentuan ;Mengkases halaman edit informasi toko;1. Mengakses website POG ;Mungkin kedepannya kami sebagai admin dapat menambahkan logo toko kami;;
;;;;;;2. Login sebagai admin toko;;;
;;;;;;3. Mengakses halaman edit informasi toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Simpan Perubahan ;;;
TS.007;TC.007.001;Pengujian Pengisian Kolom Nama Toko ;Negatif ;Input kolom nama toko lebih dari 30 huruf ;Mengakses halaman tambah toko;1. Mengakses website POG ;Seharusmya dibatasi untuk penamaan toko misal hanya 30-50 karakter saja ;;
;;;;;;2. Login sebagai super admin ;;;
;;;;;;3. Mengakses halaman tambah toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.007;TC.006.002;Pengujian Pengisian Kolom Nama Toko ;Negatif ;Input kolom nama toko dengan simbol-simbol ;Mengakses halaman tambah toko;1. Mengakses website POG ;Seharusnya penamaan toko hanya menggunakan huruf atau angka saja ;;
;;;;;;2. Login sebagai super admin ;;;
;;;;;;3. Mengakses halaman tambah toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.007;TC.007.003;Pengujian Pengisian Kolom Nomor Telepon ;Negatif ;Input kolom nomor telepon dengan huruf ;Mengakses halaman tambah toko ;1. Mengakses website POG ;Untuk kolom nomor telepon mungkin pengisian dapat dibatasi dengan angka saja;;
;;;;;;2. Login sebagai super admin ;;;
;;;;;;3. Mengakses halaman tambah toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.007;TC.007.004;Pengujian Pengisian Kolom Informasi Dalam Penambahan Toko;Negatif ;Tidak  menginputkan informasi apapun di semua kolom ;Mengakses halaman tambah toko ;1. Mengakses website POG ;Sistem otomatis memberitahu bahwa kita wajib mengisikan seluruh informasi agar toko dapat ditambahkan ;;
;;;;;;2. Login sebagai super admin ;;;
;;;;;;3. Mengakses halaman tambah toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.007;TC.007.005;Pengujian Pengisian Kolom Nomor Telepon ;Negatif ;Input kolom nomor telepon dengan lebih dari  12 angka ;Mengakses halaman tambah toko ;1. Mengakses website POG ;Untuk kolom nomor telepon bisa dibatasi sesuai dengan jumlah nomor telepon yang normal yaitu 12 angka;;
;;;;;;2. Login sebagai super admin ;;;
;;;;;;3. Mengakses halaman tambah toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Tambah ;;;
TS.007;TC.007.006;Pengujian Kolom Penambahan Gambar Logo Toko;Positif ;Input kolom logo gambar toko sesuai dengan ketentuan ;Mengkases halaman tambah toko;1. Mengakses website POG ;Mungkin kedepannya kami sebagai admin dapat menambahkan logo toko kami;;
;;;;;;2. Login sebagai super admin ;;;
;;;;;;3. Mengakses halaman tambah toko;;;
;;;;;;4. Memasukan informasi tentang toko;;;
;;;;;;5. Tekan tombol Tambah ;;;
