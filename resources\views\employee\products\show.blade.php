@extends('layouts.app')

@section('title', 'Detail Produk - ' . $product->name)

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1 class="page-title">
                    <i class="fas fa-box"></i>
                    Detail Produk
                </h1>
                <p class="page-description">Informasi lengkap produk {{ $product->name }}</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('employee.products.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="product-detail-container">
        <!-- Product Image -->
        <div class="product-image-section">
            <div class="product-image-large">
                @if($product->image)
                    <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                @else
                    <div class="no-image-large">
                        <i class="fas fa-image"></i>
                        <span>Tidak ada gambar</span>
                    </div>
                @endif
            </div>
        </div>

        <!-- Product Information -->
        <div class="product-info-section">
            <div class="info-card">
                <div class="card-header">
                    <h2>{{ $product->name }}</h2>
                    <span class="badge {{ $product->status == 'active' ? 'badge-success' : 'badge-danger' }}">
                        {{ $product->status == 'active' ? 'Aktif' : 'Tidak Aktif' }}
                    </span>
                </div>

                <div class="card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Kode Produk</label>
                            <span>{{ $product->code }}</span>
                        </div>

                        <div class="info-item">
                            <label>Kategori</label>
                            <span>{{ $product->category->name ?? 'Tanpa Kategori' }}</span>
                        </div>

                        <div class="info-item">
                            <label>Harga</label>
                            <span class="price">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                        </div>

                        <div class="info-item">
                            <label>Stok</label>
                            <span class="stock {{ $product->stock <= 10 ? 'low-stock' : '' }}">
                                {{ $product->stock }} unit
                            </span>
                        </div>

                        <div class="info-item">
                            <label>Cabang</label>
                            <span>{{ $product->branch->name ?? 'Tidak ada cabang' }}</span>
                        </div>

                        <div class="info-item">
                            <label>Tanggal Dibuat</label>
                            <span>{{ $product->created_at->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>

                    @if($product->description)
                        <div class="description-section">
                            <label>Deskripsi</label>
                            <p>{{ $product->description }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.product-detail-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1.5rem;
}

.product-image-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-image-large {
    width: 100%;
    height: 400px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image-large {
    text-align: center;
    color: #6c757d;
}

.no-image-large i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.product-info-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.card-body {
    padding: 1.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-item span {
    font-size: 1rem;
    color: #1f2937;
    font-weight: 500;
}

.price {
    color: #059669 !important;
    font-size: 1.25rem !important;
    font-weight: 700 !important;
}

.stock {
    font-weight: 600 !important;
}

.stock.low-stock {
    color: #dc2626 !important;
}

.description-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 1.5rem;
}

.description-section label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: block;
    margin-bottom: 0.75rem;
}

.description-section p {
    color: #374151;
    line-height: 1.6;
    margin: 0;
}

@media (max-width: 768px) {
    .product-detail-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .product-image-large {
        height: 250px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
</style>
@endsection
