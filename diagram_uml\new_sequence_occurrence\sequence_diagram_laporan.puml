@startuml sequence_diagram_laporan
!theme plain
title Sequence Diagram - Generate Laporan (Simplified)

' Styling sesuai referensi
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowColor #333333
skinparam sequenceActorBorderColor #333333
skinparam sequenceActorBackgroundColor #E3F2FD
skinparam sequenceParticipantBorderColor #333333
skinparam sequenceParticipantBackgroundColor #F3E5F5
skinparam sequenceGroupBackgroundColor #F8F9FA
skinparam sequenceLifeLineBorderColor #6C757D
skinparam sequenceLifeLineBackgroundColor #FFFFFF

' Actors dan Participants (1 actor + 3 objects)
actor "👤 Direktur" as Direktur
participant "🖥️ UI" as UI
participant "🌐 Controller" as Controller
participant "🗄️ Database" as Database

' Sequence Flow
Direktur -> UI: 1. A<PERSON><PERSON>
activate UI
UI -> Controller: :order(GET /reports)
activate Controller
Controller -> Database: :order(SELECT user_role, permissions)
activate Database
Database --> Controller: :order(role data)
deactivate Database
Controller --> UI: :order(report menu)
deactivate Controller
UI --> Direktur: :order(tampilkan menu laporan)
deactivate UI

Direktur -> UI: 2. <PERSON><PERSON><PERSON>
activate UI
UI -> Controller: :order(GET /reports/{type})
activate Controller
Controller --> UI: :order(report form)
deactivate Controller
UI --> Direktur: :order(tampilkan form filter)
deactivate UI

Direktur -> UI: 3. Generate Laporan
activate UI
UI -> Controller: :order(POST /reports/generate)
activate Controller
Controller -> Database: :order(SELECT data WITH filters)
activate Database
Database --> Controller: :order(report data)
deactivate Database
Controller --> UI: :order(generated report)
deactivate Controller
UI --> Direktur: :order(tampilkan/download laporan)
deactivate UI

note right of Direktur
  **Implementasi Laravel:**
  - ReportController
  - Transaction Model
  - Product Model
  - Export Libraries
end note

@enduml
