<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Sidebar</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%);
            min-height: 100vh;
        }

        /* Mobile Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100vw;
            height: 50px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
        }

        .mobile-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: -300px;
            width: 300px;
            height: 100vh;
            background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            z-index: 1002;
            transition: left 0.3s ease;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.3);
            color: white;
        }

        .sidebar.mobile-open {
            left: 0;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .nav-list {
            list-style: none;
            padding: 20px 0;
        }

        .nav-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .nav-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        /* Overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1001;
            display: none;
        }

        .mobile-overlay.show {
            display: block;
        }

        /* Main Content */
        .main-content {
            padding: 70px 20px 20px 20px;
        }

        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">TEST CATAT</h1>
        <div style="width: 32px;"></div>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <h2>CATAT</h2>
            <p>Test Sidebar</p>
        </div>
        <ul class="nav-list">
            <a href="#" class="nav-item" onclick="closeMobileMenu()">
                <i class="fas fa-home"></i>
                <span class="nav-text">Dashboard</span>
            </a>
            <a href="#" class="nav-item" onclick="closeMobileMenu()">
                <i class="fas fa-users"></i>
                <span class="nav-text">Pegawai</span>
            </a>
            <a href="#" class="nav-item" onclick="closeMobileMenu()">
                <i class="fas fa-store"></i>
                <span class="nav-text">Cabang</span>
            </a>
            <a href="#" class="nav-item" onclick="closeMobileMenu()">
                <i class="fas fa-box"></i>
                <span class="nav-text">Produk</span>
            </a>
            <a href="#" class="nav-item" onclick="closeMobileMenu()">
                <i class="fas fa-chart-line"></i>
                <span class="nav-text">Laporan</span>
            </a>
            <a href="#" class="nav-item" onclick="closeMobileMenu()">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Logout</span>
            </a>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="test-card">
            <h2>Test Mobile Sidebar</h2>
            <p>Klik tombol hamburger di header untuk membuka sidebar.</p>
            <button class="test-btn" onclick="toggleMobileMenu()">Toggle Sidebar</button>
            <button class="test-btn" onclick="testSidebar()">Test Manual</button>
        </div>

        <div class="test-card">
            <h3>Debug Info</h3>
            <div id="debug-info"></div>
        </div>
    </main>

    <script>
        function toggleMobileMenu() {
            console.log('🔄 Toggle mobile menu called');
            
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (!sidebar || !overlay) {
                console.error('❌ Elements not found:', { sidebar: !!sidebar, overlay: !!overlay });
                updateDebugInfo('Error: Elements not found');
                return;
            }

            const isOpen = sidebar.classList.contains('mobile-open');
            
            if (isOpen) {
                console.log('🔒 Closing sidebar');
                sidebar.classList.remove('mobile-open');
                overlay.classList.remove('show');
                if (menuBtn) menuBtn.className = 'fas fa-bars';
                document.body.style.overflow = '';
                updateDebugInfo('Sidebar closed');
            } else {
                console.log('🔓 Opening sidebar');
                sidebar.classList.add('mobile-open');
                overlay.classList.add('show');
                if (menuBtn) menuBtn.className = 'fas fa-times';
                document.body.style.overflow = 'hidden';
                updateDebugInfo('Sidebar opened');
            }
        }

        function closeMobileMenu() {
            console.log('🔒 Force closing mobile menu');
            
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (sidebar) sidebar.classList.remove('mobile-open');
            if (overlay) overlay.classList.remove('show');
            if (menuBtn) menuBtn.className = 'fas fa-bars';
            document.body.style.overflow = '';
            updateDebugInfo('Sidebar force closed');
        }

        function testSidebar() {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.style.left = sidebar.style.left === '0px' ? '-300px' : '0px';
                updateDebugInfo('Manual test: ' + sidebar.style.left);
            }
        }

        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debug-info');
            const time = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<p>${time}: ${message}</p>`;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('Page loaded');
            
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            updateDebugInfo(`Elements found: sidebar=${!!sidebar}, overlay=${!!overlay}, menuBtn=${!!menuBtn}`);
        });
    </script>
</body>
</html>
