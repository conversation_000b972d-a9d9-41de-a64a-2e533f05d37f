<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Android Sidebar Debug</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f9ff;
            overflow-x: hidden;
            -webkit-text-size-adjust: 100%;
        }

        /* Mobile Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            z-index: 1002;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            padding: 15px;
            cursor: pointer;
            border-radius: 8px;
            -webkit-tap-highlight-color: rgba(255,255,255,0.1);
        }

        .mobile-menu-btn:active {
            background: rgba(255,255,255,0.1);
        }

        .mobile-title {
            font-size: 20px;
            font-weight: 700;
        }

        /* Sidebar - ANDROID OPTIMIZED */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 300px;
            height: 100vh;
            background: #1e40af;
            z-index: 1001;
            transform: translateX(-100%);
            -webkit-transform: translateX(-100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: white;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            will-change: transform;
            backface-visibility: hidden;
            box-shadow: 2px 0 20px rgba(0,0,0,0.3);
        }

        .sidebar.active {
            transform: translateX(0);
            -webkit-transform: translateX(0);
        }

        .sidebar-header {
            padding: 30px 20px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            background: rgba(0,0,0,0.1);
        }

        .sidebar-header h2 {
            color: white;
            font-size: 24px;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }

        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            gap: 18px;
            font-size: 16px;
            font-weight: 500;
            transition: background 0.2s ease;
            -webkit-tap-highlight-color: rgba(255,255,255,0.1);
            min-height: 60px;
        }

        .nav-item:hover,
        .nav-item:active {
            background: rgba(255,255,255,0.15);
        }

        .nav-item i {
            font-size: 18px;
            width: 24px;
            text-align: center;
            color: white;
            flex-shrink: 0;
        }

        .nav-text {
            color: white;
            font-size: 16px;
            font-weight: 500;
            flex: 1;
        }

        /* Overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-overlay.active {
            display: block;
            opacity: 1;
        }

        /* Main Content */
        .main-content {
            padding: 80px 20px 20px 20px;
            min-height: 100vh;
        }

        .debug-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .debug-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            font-weight: 500;
            min-height: 50px;
            -webkit-tap-highlight-color: rgba(59, 130, 246, 0.3);
        }

        .debug-btn:active {
            background: #2563eb;
            transform: scale(0.98);
        }

        .floating-debug {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #ff4444;
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 24px;
            cursor: pointer;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(255, 68, 68, 0.4);
            -webkit-tap-highlight-color: rgba(255, 68, 68, 0.3);
        }

        .floating-debug:active {
            transform: scale(0.95);
        }

        #debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">ANDROID DEBUG</h1>
        <div style="width: 32px;"></div>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <h2>CATAT</h2>
            <p>Android Test</p>
        </div>
        <ul class="nav-list">
            <a href="#" class="nav-item" onclick="logClick('Dashboard')">
                <i class="fas fa-home"></i>
                <span class="nav-text">Dashboard</span>
            </a>
            <a href="#" class="nav-item" onclick="logClick('Pegawai')">
                <i class="fas fa-users"></i>
                <span class="nav-text">Pegawai</span>
            </a>
            <a href="#" class="nav-item" onclick="logClick('Cabang')">
                <i class="fas fa-store"></i>
                <span class="nav-text">Cabang</span>
            </a>
            <a href="#" class="nav-item" onclick="logClick('Produk')">
                <i class="fas fa-box"></i>
                <span class="nav-text">Produk</span>
            </a>
            <a href="#" class="nav-item" onclick="logClick('Laporan')">
                <i class="fas fa-chart-line"></i>
                <span class="nav-text">Laporan</span>
            </a>
            <a href="#" class="nav-item" onclick="logClick('Logout')">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Logout</span>
            </a>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="debug-card">
            <h2>Android Sidebar Debug</h2>
            <p>Test sidebar functionality on Android devices.</p>
            <button class="debug-btn" onclick="toggleSidebar()">Toggle Sidebar</button>
            <button class="debug-btn" onclick="forceShow()">Force Show</button>
            <button class="debug-btn" onclick="analyzeElements()">Analyze</button>
        </div>

        <div class="debug-card">
            <h3>Debug Info</h3>
            <div id="debug-info">Ready for testing...</div>
        </div>
    </main>

    <!-- Floating Debug Button -->
    <button class="floating-debug" onclick="emergencyToggle()">🔧</button>

    <script>
        let debugLog = [];

        function toggleSidebar() {
            log('🔄 Toggle sidebar called');
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');
            
            if (!sidebar) {
                log('❌ Sidebar not found');
                return;
            }

            const isOpen = sidebar.classList.contains('active');
            log(`Current state: ${isOpen ? 'OPEN' : 'CLOSED'}`);
            
            if (isOpen) {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
                if (menuBtn) menuBtn.className = 'fas fa-bars';
                log('🔒 Sidebar closed');
            } else {
                sidebar.classList.add('active');
                overlay.classList.add('active');
                if (menuBtn) menuBtn.className = 'fas fa-times';
                log('🔓 Sidebar opened');
                
                // Force content visible
                setTimeout(() => forceContentVisible(), 100);
            }
        }

        function closeSidebar() {
            log('🔒 Close via overlay');
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (sidebar) sidebar.classList.remove('active');
            if (overlay) overlay.classList.remove('active');
            if (menuBtn) menuBtn.className = 'fas fa-bars';
        }

        function forceShow() {
            log('👁️ Force show sidebar');
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.classList.add('active');
                sidebar.style.transform = 'translateX(0)';
                sidebar.style.webkitTransform = 'translateX(0)';
                forceContentVisible();
            }
        }

        function forceContentVisible() {
            log('🔧 Forcing content visible');
            const sidebar = document.querySelector('.sidebar');
            if (!sidebar) return;

            // Force all elements visible
            const allElements = sidebar.querySelectorAll('*');
            allElements.forEach(el => {
                if (el.classList.contains('nav-text')) {
                    el.style.display = 'inline';
                    el.style.color = 'white';
                    el.style.fontSize = '16px';
                    el.style.visibility = 'visible';
                    el.style.opacity = '1';
                }
            });
        }

        function analyzeElements() {
            log('🔍 Analyzing elements');
            const sidebar = document.querySelector('.sidebar');
            const navItems = document.querySelectorAll('.nav-item');
            const navTexts = document.querySelectorAll('.nav-text');
            
            const analysis = {
                userAgent: navigator.userAgent.substring(0, 100),
                isAndroid: /Android/i.test(navigator.userAgent),
                screenSize: `${window.innerWidth}x${window.innerHeight}`,
                sidebarExists: !!sidebar,
                sidebarClasses: sidebar ? sidebar.className : 'none',
                navItemsCount: navItems.length,
                navTextsCount: navTexts.length,
                sidebarTransform: sidebar ? getComputedStyle(sidebar).transform : 'none'
            };
            
            log('📊 Analysis:', JSON.stringify(analysis, null, 2));
        }

        function emergencyToggle() {
            log('🚨 Emergency toggle');
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                const isVisible = sidebar.style.transform === 'translateX(0px)';
                sidebar.style.transform = isVisible ? 'translateX(-100%)' : 'translateX(0px)';
                sidebar.style.webkitTransform = isVisible ? 'translateX(-100%)' : 'translateX(0px)';
                if (!isVisible) forceContentVisible();
            }
        }

        function logClick(item) {
            log(`📱 Clicked: ${item}`);
            closeSidebar();
        }

        function log(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `${timestamp}: ${message}`;
            if (data) logEntry += '\n' + JSON.stringify(data, null, 2);
            
            debugLog.push(logEntry);
            console.log(logEntry);
            
            const debugInfo = document.getElementById('debug-info');
            debugInfo.textContent = debugLog.slice(-10).join('\n\n');
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Android debug page loaded');
            analyzeElements();
        });

        // Touch event logging
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('.mobile-menu-btn')) {
                log('👆 Touch start on menu button');
            }
        });
    </script>
</body>
</html>
