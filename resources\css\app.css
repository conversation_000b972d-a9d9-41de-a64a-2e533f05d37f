/* Removed Tailwind directives to disable Tailwind CSS */
/* @tailwind base; */
/* @tailwind components; */
/* @tailwind utilities; */

/* Base styles */
:root {
    --primary-dark: #1e40af;
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --secondary: #0ea5e9;
    --accent: #06b6d4;
    --light: #f0f9ff;
    --white: #ffffff;
    --gray-light: #f1f5f9;
    --gray: #94a3b8;
    --gray-dark: #475569;
    --text-primary: #1e293b;
    --text-secondary: #334155;
    --text-muted: #64748b;
    --success: #059669;
    --warning: #d97706;
    --danger: #dc2626;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.06);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.08);
    --radius: 16px;
    --radius-sm: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --bg-gradient: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none !important; /* Remove all underlines globally */
}

/* Remove underlines from all links and buttons */
a, button, .btn, .nav-item, .action-link, .action-card {
    text-decoration: none !important;
}

a:hover, button:hover, .btn:hover, .nav-item:hover, .action-link:hover, .action-card:hover {
    text-decoration: none !important;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-gradient);
    background-attachment: fixed;
    color: var(--text-primary);
    min-height: 100vh;
    display: block;
    justify-content: normal; /* Reset centering */
    align-items: normal; /* Reset centering */
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Navigation */
.nav {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    padding: 1rem 0;
    color: white;
}

/* Auth Pages Specific Styles */
/* Keeping auth styles for login/register pages */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 2rem 0;
    min-height: 100vh; /* Ensure auth page takes full height */
}

.auth-card {
    background: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    text-align: center;
    color: var(--text-primary); /* Override forced white font color */
}

.auth-card h2 {
    font-size: 1.875rem; /* text-3xl */
    font-weight: 800; /* font-extrabold */
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.auth-card p {
    font-size: 0.875rem; /* text-sm */
    color: var(--text-muted); /* gray-600 */
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.auth-card .link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500; /* font-medium */
    transition: color 0.2s ease-in-out;
}

.auth-card .link:hover {
    color: var(--secondary-color);
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
    text-align: left;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.form-group:hover label {
    color: var(--primary);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    transition: var(--transition);
    color: #1e293b;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 0.95);
}

/* Enhanced Select and Dropdown Styling */
select.form-control, select, .filter-select {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    padding: 14px 45px 14px 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    transition: var(--transition);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-position: right 14px center;
    background-repeat: no-repeat;
    background-size: 16px;
    min-width: 180px;
    height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    line-height: 1.2;
}

select.form-control:focus, select:focus, .filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

select.form-control:hover, select:hover, .filter-select:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Dropdown option styling */
select option {
    background: white;
    color: var(--text-primary);
    padding: 12px 16px;
    font-weight: 500;
    border: none;
}

select option:checked {
    background: var(--primary);
    color: white;
}

select option:hover {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary);
}

/* Custom Dropdown Container */
.dropdown-container {
    position: relative;
    display: inline-block;
}

.dropdown-button {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    padding: 12px 40px 12px 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 150px;
}

.dropdown-button:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.dropdown-button:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dropdown-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    transition: var(--transition);
    color: var(--text-muted);
}

.dropdown-button.open .dropdown-arrow {
    transform: translateY(-50%) rotate(180deg);
    color: var(--primary);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 4px;
}

.dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary);
}

.dropdown-item.selected {
    background: rgba(59, 130, 246, 0.15);
    color: var(--primary);
    font-weight: 600;
}

/* Enhanced Date Input Styling */
input[type="date"] {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    padding: 14px 45px 14px 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    transition: var(--transition);
    position: relative;
    cursor: pointer;
    min-width: 180px;
    height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

input[type="date"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

input[type="date"]:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Custom date picker icon */
input[type="date"]::-webkit-calendar-picker-indicator {
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233b82f6'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e") no-repeat;
    background-size: 20px 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
    transition: var(--transition);
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

/* Hide default date format when empty */
input[type="date"]:invalid {
    color: var(--text-muted);
}

input[type="date"]:valid {
    color: var(--text-primary);
}

/* Date input wrapper for better positioning */
.date-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.date-input-wrapper::before {
    content: attr(data-placeholder);
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 14px;
    pointer-events: none;
    transition: var(--transition);
    opacity: 1;
}

.date-input-wrapper.has-value::before,
.date-input-wrapper.focused::before {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
}

input[type="date"]:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

/* Custom date picker icon */
input[type="date"]::-webkit-calendar-picker-indicator {
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233b82f6'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e") no-repeat;
    background-size: 18px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Date input placeholder styling */
input[type="date"]:invalid {
    color: #9ca3af;
}

input[type="date"]:valid {
    color: var(--text-primary);
}

/* Enhanced date input wrapper */
.date-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.date-input-wrapper::before {
    content: attr(data-placeholder);
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 14px;
    pointer-events: none;
    transition: var(--transition);
    opacity: 1;
}

.date-input-wrapper.has-value::before,
.date-input-wrapper.focused::before {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
}

/* Custom date input styling */
.custom-date-input {
    position: relative;
    overflow: hidden;
}

.custom-date-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: var(--transition);
    pointer-events: none;
}

.custom-date-input:focus::before {
    left: 100%;
}

/* Date tooltip styling */
.date-tooltip {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%) !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: var(--radius-sm) !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-top: 6px !important;
    box-shadow: var(--shadow) !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
}

/* Filter form improvements */
.filter-form {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group input[type="date"] {
    min-width: 180px;
}

/* Action header layout */
.action-header {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-header .filters {
    flex: 1;
}

/* Form controls consistent styling */
.filter-group .form-control {
    min-width: 150px;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.filter-group .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.filter-group input[type="date"].form-control {
    min-width: 160px;
}

/* Fix dropdown arrow duplication */
.filter-group select.form-control {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

/* Global select styling to ensure consistency */
select {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
}

/* Ensure all selects have proper arrow */
select:not([multiple]) {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 16px !important;
    padding-right: 40px !important;
}

/* Override any inline styles that might cause issues */
select[style*="background-image"] {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
}

/* Specific classes for different select types */
.form-select, .form-control, .product-dropdown {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 16px !important;
    padding-right: 40px !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .action-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-form {
        flex-direction: column;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group .form-control {
        min-width: auto;
        width: 100%;
    }
}

/* Enhanced search input styling */
.search-box {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 300px;
}

.search-box input {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    padding: 14px 16px 14px 45px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    transition: var(--transition);
    width: 100%;
    height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    line-height: 1.2;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.search-box input:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary);
    font-size: 16px;
    z-index: 1;
    transition: var(--transition);
}

.search-box input:focus + i,
.search-box:hover i {
    color: var(--primary-dark);
    transform: translateY(-50%) scale(1.1);
}

/* Filter form improvements */
.filter-form input[type="text"] {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    padding: 14px 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    transition: var(--transition);
    min-width: 200px;
    height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    line-height: 1.2;
}

.filter-form input[type="text"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.filter-form input[type="text"]:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Action header improvements */
.action-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.search-filter {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

/* Filter group layout */
.filter-group {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
}

.filter-group > * {
    flex-shrink: 0;
}

.filter-group select,
.filter-group input[type="text"] {
    min-width: 180px;
}

/* Responsive filter layout */
@media (max-width: 768px) {
    .filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .filter-group select,
    .filter-group input[type="text"] {
        min-width: 100%;
    }

    .action-header {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filter {
        justify-content: center;
    }
}

/* Button improvements */
.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: var(--radius-sm);
}

.btn-secondary {
    background: rgba(100, 116, 139, 0.1);
    color: var(--text-primary);
    border: 2px solid rgba(100, 116, 139, 0.2);
    font-weight: 600;
}

.btn-secondary:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline {
    background: transparent;
    color: var(--text-muted);
    border: 2px solid rgba(100, 116, 139, 0.3);
    font-weight: 600;
}

.btn-outline:hover {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger);
    border-color: var(--danger);
    transform: translateY(-1px);
}

.text-danger {
    color: #dc2626; /* red-600 - Make error text red */
    font-size: 0.75rem; /* text-xs */
    margin-top: 0.25rem; /* mt-1 */
}

.alert {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.alert-danger {
    background-color: #fee2e2; /* red-100 */
    color: #dc2626; /* red-600 - Make error text red */
    border: 1px solid #fca5a5; /* red-300 */
}

/* Checkbox */
.form-checkbox {
    appearance: none;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: var(--primary-color);
    border-radius: 0.25rem;
    border-width: 1px;
    border-color: var(--border-color);
    background-color: var(--background-color);
}

.form-checkbox:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

/* Role Selection Buttons */
.role-selection {
    display: flex;
    gap: 0.5rem; /* space-x-2 */
    margin-top: 0.5rem; /* mt-2 */
}

/* Auth page specific dropdown styling */
.auth-container select.form-control {
    width: 100%;
    padding: 14px 45px 14px 16px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-position: right 14px center;
    background-repeat: no-repeat;
    background-size: 16px;
    height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    line-height: 1.2;
}

.auth-container select.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.auth-container select.form-control:hover {
    border-color: rgba(59, 130, 246, 0.4);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.btn-role {
    flex-grow: 1;
    padding: 0.75rem 1rem; /* py-3 px-4 */
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    color: var(--text-color);
    font-weight: 500; /* font-medium */
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-align: center;
}

/* Distinct colors for each role button */
.btn-role[data-role="manager"],
.btn-role[data-role="employee"],
.btn-role[data-role="director"] {
    background-color: #93c5fd; /* light blue */
    color: #1e3a8a; /* dark blue text */
    border-color: #93c5fd;
}

.btn-role[data-role="manager"]:hover,
.btn-role[data-role="employee"]:hover,
.btn-role[data-role="director"]:hover {
    background-color: #bfdbfe; /* lighter blue on hover */
    border-color: #bfdbfe;
}

.btn-role.selected {
    background-color: #3b82f6 !important; /* blue-500 */
    color: white !important;
    border-color: #3b82f6 !important;
    opacity: 1 !important; /* Ensure fully visible */
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.7); /* Add glow effect */
}

/* Remove previous hover and selected styles */
.btn-role:hover {
    filter: none;
}


.btn-role.hidden {
    display: none;
}

/* Buttons */
.btn {
    padding: 0.625rem 1rem; /* py-2 px-4 */
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 1rem; /* text-sm */
    font-weight: 500; /* font-medium */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: var(--radius-sm);
    border: none;
    transition: var(--transition);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

.w-full {
    width: 100%;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
}

.table th {
    background-color: #f9fafb;
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
}

/* Modern Index Page Styles */
.page-header {
    background: linear-gradient(135deg, var(--white) 0%, var(--light) 100%);
    border-radius: var(--radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow);
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.header-text {
    flex: 1;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-dark);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title i {
    color: var(--primary);
}

.page-subtitle {
    font-size: 16px;
    color: var(--gray-dark);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius);
    padding: 24px;
    box-shadow: var(--shadow);
    border: 1px solid rgba(37, 99, 235, 0.1);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 24px;
}

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-dark);
    margin: 0 0 4px 0;
}

.stat-content p {
    font-size: 14px;
    color: var(--gray-dark);
    margin: 0;
}

/* Filter Section */
.filter-section {
    margin-bottom: 24px;
}

.filter-card {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    border: 1px solid rgba(37, 99, 235, 0.1);
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, var(--light) 0%, var(--white) 100%);
    padding: 16px 24px;
    border-bottom: 1px solid rgba(37, 99, 235, 0.1);
}

.filter-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-content {
    padding: 24px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-dark);
}

/* Table Card */
.table-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: var(--transition);
}

.table-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.table-header {
    background: linear-gradient(135deg, var(--light) 0%, var(--white) 100%);
    padding: 20px 24px;
    border-bottom: 1px solid rgba(37, 99, 235, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.table-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.table-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: var(--gray-dark);
    z-index: 1;
}

.search-box input {
    padding-left: 40px;
    min-width: 250px;
}

/* Modern Table */
.table-container {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.modern-table thead {
    background: linear-gradient(135deg, var(--gray-light) 0%, var(--white) 100%);
}

.modern-table th {
    padding: 16px 20px;
    text-align: left;
    font-weight: 600;
    color: var(--primary-dark);
    border-bottom: 2px solid rgba(37, 99, 235, 0.1);
    white-space: nowrap;
}

.modern-table th i {
    margin-right: 8px;
    color: var(--primary);
}

.modern-table td {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(37, 99, 235, 0.05);
    vertical-align: middle;
}

.modern-table tbody tr:hover {
    background: rgba(37, 99, 235, 0.02);
}

/* User/Branch Info Components */
.user-info, .branch-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar, .branch-avatar {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 16px;
}

.user-details, .branch-details {
    flex: 1;
}

.user-details h4, .branch-details h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0 0 2px 0;
}

.user-id, .branch-id {
    font-size: 12px;
    color: var(--gray-dark);
}

.email-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray-dark);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: var(--gray-dark);
}

.address-info {
    position: relative;
}

.address-info p {
    margin: 0;
    color: var(--gray-dark);
    line-height: 1.4;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.badge-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary);
}

.badge-info {
    background: rgba(14, 165, 233, 0.1);
    color: var(--secondary);
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.badge-gray {
    background: rgba(100, 116, 139, 0.1);
    color: var(--gray-dark);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    min-width: auto;
}

.btn-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.btn-warning:hover {
    background: var(--warning);
    color: var(--white);
}

.btn-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.btn-danger:hover {
    background: var(--danger);
    color: var(--white);
}

.btn-secondary {
    background: rgba(100, 116, 139, 0.1);
    color: var(--gray-dark);
    border: 1px solid rgba(100, 116, 139, 0.2);
}

.btn-secondary:hover {
    background: var(--gray-dark);
    color: var(--white);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.empty-content i {
    font-size: 48px;
    color: var(--gray);
}

.empty-content h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-dark);
    margin: 0;
}

.empty-content p {
    font-size: 14px;
    color: var(--gray-dark);
    margin: 0;
}

/* Pagination */
.pagination-wrapper {
    padding: 20px 24px;
    border-top: 1px solid rgba(37, 99, 235, 0.1);
    background: var(--light);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(37, 99, 235, 0.1);
    background: linear-gradient(135deg, var(--light) 0%, var(--white) 100%);
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-body {
    padding: 24px;
}

.modal-body p {
    margin: 0 0 12px 0;
    color: var(--gray-dark);
    line-height: 1.5;
}

.warning-text {
    color: var(--warning);
    font-weight: 500;
    font-size: 14px;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid rgba(37, 99, 235, 0.1);
    background: var(--light);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Tooltip */
.tooltip {
    position: relative;
    cursor: help;
    color: var(--primary);
}

.tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-dark);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 100;
    margin-bottom: 5px;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--primary-dark);
}

/* Alert Improvements */
.alert {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    border-radius: var(--radius);
    margin-bottom: 24px;
    border: 1px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-icon {
    font-size: 20px;
    margin-top: 2px;
}

.alert-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.alert-content p {
    font-size: 14px;
    margin: 0;
    opacity: 0.9;
}

/* Text utilities */
.text-muted {
    color: var(--gray-dark);
    font-style: italic;
}

/* Utilities */
/* Removed utility classes to rely on conventional styling */
/*
.text-center { text-align: center; }
.mt-4 { margin-top: 1rem; }
.mb-4 { margin-bottom: 1rem; }
.p-4 { padding: 1rem; }
*/

/* Add styles for the dashboard */
.dashboard-container {
    padding: 3rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--card-background);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.dashboard-card h3 {
    font-size: 1.125rem;
    font-weight: 500;
    margin-top: 0;
    margin-bottom: 0.5rem;
}

.dashboard-card p {
    font-size: 2.25rem;
    font-weight: 700;
    margin: 0;
}

.recent-transactions {
    margin-top: 2rem;
}

.recent-transactions h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
}

.transaction-list {
    background: var(--card-background);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info p {
    margin: 0;
    font-size: 0.875rem;
}

.transaction-info .font-medium {
    font-weight: 500;
    color: #1f2937;
}

.transaction-amount p {
     margin: 0;
     font-size: 0.875rem;
     text-align: right;
}

.transaction-amount .font-medium {
     font-weight: 500;
     color: #1f2937;
}

/* Sidebar Styles */
body > aside.sidebar {
    width: 300px;
    background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    backdrop-filter: blur(10px);
    color: var(--white);
    height: 100vh;
    position: fixed;
    padding: 0;
    box-shadow: var(--shadow-xl);
    z-index: 100;
    transition: var(--transition);
    overflow-y: auto;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Main Content Styles */
body > main.main-content {
    margin-left: 300px;
    padding: 32px;
    transition: var(--transition);
    overflow: hidden;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 300px;
    background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    backdrop-filter: blur(10px);
    color: var(--white);
    height: 100vh;
    position: fixed;
    padding: 0;
    box-shadow: var(--shadow-xl);
    z-index: 100;
    transition: var(--transition);
    overflow-y: auto;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .nav-item {
    color: white !important;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    text-decoration: none !important;
}

.logo img {
    border-radius: var(--radius-sm);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.logo img:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.welcome-message {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    padding: 12px 16px;
    margin-top: 16px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-icon {
    font-size: 28px;
    color: var(--white);
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--white);
}

.nav-list {
    padding: 0 12px 24px;
}

.nav-item {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border-radius: var(--radius-sm);
    margin-bottom: 4px;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--accent);
    transform: scaleY(0);
    transition: var(--transition);
}

.nav-item:hover::before, .nav-item.active::before {
    transform: scaleY(1);
}

.nav-item:hover, .nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item i {
    font-size: 18px;
    width: 20px;
    text-align: center;
    transition: var(--transition);
}

.nav-item:hover i {
    transform: scale(1.1);
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: 300px;
    padding: 32px;
    transition: var(--transition);
    min-height: 100vh;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 28px;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-title h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-dark);
}

.page-title p {
    font-size: 14px;
    color: var(--gray-dark);
    margin-top: 4px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-details {
    text-align: right;
}

.user-details .name {
    font-weight: 600;
    color: var(--primary-dark);
}

.user-details .role {
    font-size: 14px;
    color: var(--gray-dark);
}

.avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 18px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius);
    padding: 28px;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-light), var(--accent));
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

/* Specific stat card colors */
.stat-card:nth-child(1)::before {
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
}
.stat-card:nth-child(2)::before {
    background: linear-gradient(to bottom, #10b981, #059669);
}
.stat-card:nth-child(3)::before {
    background: linear-gradient(to bottom, #f59e0b, #d97706);
}
.stat-card:nth-child(4)::before {
    background: linear-gradient(to bottom, #8b5cf6, #7c3aed);
}

/* Stat icon colors */
.stat-icon.branches {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}
.stat-icon.sales {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}
.stat-icon.transactions {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}
.stat-icon.performance {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-dark);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--gray-dark);
    margin-bottom: 4px;
    font-weight: 500;
}

.stat-change {
    font-size: 12px;
    color: var(--success);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.negative {
    color: var(--danger);
}

.stat-change.positive {
    color: var(--success);
}

.stat-change i {
    font-size: 10px;
}

/* Charts Section */
.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius);
    padding: 28px;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
}

.chart-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-dark);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Recent Activity */
.activity-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius);
    padding: 28px;
    box-shadow: var(--shadow-lg);
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
}

.activity-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.activity-list {
    list-style: none;
}

.activity-item {
    display: flex;
    padding: 16px 0;
    border-bottom: 1px solid var(--gray-light);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--primary);
    font-size: 18px;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.activity-desc {
    font-size: 14px;
    color: var(--gray-dark);
}

.activity-time {
    font-size: 12px;
    color: var(--gray-dark);
    text-align: right;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 80px;
    }
    .sidebar .logo-text,
    .sidebar .nav-text,
    .sidebar .welcome-message {
        display: none;
    }
    .sidebar .nav-item {
        justify-content: center;
        padding: 20px;
        margin: 0 8px 4px;
    }
    .sidebar .logo img {
        height: 60px;
    }
    .main-content {
        margin-left: 80px;
    }
    body > main.main-content {
        margin-left: 80px;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    .top-bar {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    .user-info {
        width: 100%;
        justify-content: space-between;
    }
    .main-content {
        padding: 60px 16px 24px 16px !important;
        margin-left: 0 !important;
    }

    /* Show mobile header */
    .mobile-header {
        display: flex !important;
    }

    /* Mobile sidebar - defer to layout.blade.php styles */
    .sidebar {
        /* Layout.blade.php will handle mobile styles */
    }
}

/* Additional Modern Enhancements */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.filter-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Smooth scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Loading animation for cards */
@keyframes cardLoad {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .stat-card, .chart-card, .activity-card, .table-card {
    animation: cardLoad 0.6s ease-out;
}

/* Pulse effect for important elements */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.stat-card .stat-icon {
    animation: pulse 2s infinite;
}

/* Action Cards Styling */
.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 24px;
}

.action-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 24px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 16px;
    text-decoration: none !important;
    color: inherit;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: var(--transition);
}

.action-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-xl);
    text-decoration: none !important;
    color: inherit;
    border-color: rgba(59, 130, 246, 0.3);
}

.action-card:hover::before {
    left: 100%;
}

.action-icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--primary);
    transition: var(--transition);
}

.action-card:hover .action-icon {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    transform: scale(1.1);
}

.action-title {
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 4px;
    font-size: 16px;
}

/* Primary action card styling */
.action-card.primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    border: none;
}

.action-card.primary .action-title {
    color: white;
    font-weight: 700;
}

.action-card.primary .action-desc {
    color: rgba(255, 255, 255, 0.9);
}

.action-card.primary .action-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.action-card.primary:hover .action-icon {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: scale(1.1);
}

.action-card.primary:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 20px 40px rgba(37, 99, 235, 0.3);
}

.action-desc {
    font-size: 14px;
    color: var(--gray-dark);
    line-height: 1.4;
}

/* Activity Items Styling */
.activity-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(5px);
    border-radius: var(--radius-sm);
    margin-bottom: 16px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: var(--transition);
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateX(4px);
    box-shadow: var(--shadow);
}

.activity-item .activity-icon {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: var(--shadow);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 4px;
    font-size: 15px;
}

.activity-desc {
    font-size: 13px;
    color: var(--gray-dark);
    margin-bottom: 4px;
    line-height: 1.4;
}

.activity-amount {
    font-weight: 600;
    color: var(--success);
    font-size: 14px;
}

.activity-time {
    font-size: 12px;
    color: var(--gray);
    text-align: right;
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--gray-dark);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--gray);
    opacity: 0.5;
}

/* Quick Actions Section */
.quick-actions {
    margin-bottom: 32px;
}

.quick-actions h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.quick-actions h2 i {
    color: var(--primary);
    font-size: 20px;
}

/* Recent Activity Section */
.recent-activity h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.recent-activity h2 i {
    color: var(--primary);
    font-size: 20px;
}

/* Container improvements */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Text contrast improvements */
.text-primary {
    color: var(--text-primary) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Ensure good contrast for all text elements */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-weight: 600;
}

p, span, div {
    color: var(--text-secondary);
}

/* Table text improvements */
.modern-table th {
    color: var(--text-primary);
    font-weight: 600;
}

.modern-table td {
    color: var(--text-secondary);
}

/* Form text improvements */
.form-help {
    color: var(--text-muted);
    font-size: 12px;
    margin-top: 4px;
}

/* Button text improvements */
.btn {
    font-weight: 600;
}

/* Card title improvements */
.card-title, .table-title, .filter-header h3 {
    color: var(--text-primary) !important;
    font-weight: 600;
}

/* Ensure dropdown text is visible */
.dropdown-item {
    color: var(--text-secondary);
}

.dropdown-item:hover {
    color: var(--text-primary);
}

/* Status badge text improvements */
.badge {
    font-weight: 600;
    font-size: 12px;
}

/* Navigation text improvements */
.nav-text {
    font-weight: 500;
    color: white;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    padding: 16px 20px;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #10b981;
}

.toast.error {
    border-left-color: #ef4444;
}

.toast.warning {
    border-left-color: #f59e0b;
}

.toast.info {
    border-left-color: #3b82f6;
}

.toast-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.toast.success .toast-icon {
    color: #10b981;
}

.toast.error .toast-icon {
    color: #ef4444;
}

.toast.warning .toast-icon {
    color: #f59e0b;
}

.toast.info .toast-icon {
    color: #3b82f6;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.toast-message {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    font-size: 16px;
    color: #9ca3af;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background: #f3f4f6;
    color: #374151;
}

/* ===== MOBILE RESPONSIVE DESIGN ===== */

/* Mobile First Approach - Base styles for mobile */
@media (max-width: 768px) {
    /* Container adjustments */
    .container {
        padding: 0 10px;
        max-width: 100%;
        width: 100%;
        box-sizing: border-box;
    }

    /* Body adjustments for mobile */
    body {
        overflow-x: hidden !important;
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Main content mobile fixes */
    .main-content {
        width: 100% !important;
        max-width: 100vw !important;
        margin-left: 0 !important;
        padding: 10px !important;
        box-sizing: border-box !important;
        overflow-x: hidden !important;
    }

    /* Top bar responsive */
    .top-bar {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        margin: 0;
    }

    .page-title h1 {
        font-size: 1.5rem;
    }

    .page-title p {
        font-size: 0.875rem;
    }

    .user-info {
        order: -1;
        justify-content: center;
    }

    /* Navigation responsive - handled by layout.blade.php */

    /* Main content adjustments */
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    /* Cards responsive */
    .card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }

    .card-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Stats grid responsive */
    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
    }

    .stat-card {
        padding: 1rem !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 0 1rem 0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        word-wrap: break-word !important;
    }

    .stat-card .stat-content {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
    }

    .stat-card .stat-value {
        font-size: 1.5rem !important;
        word-break: break-word !important;
        overflow-wrap: break-word !important;
    }

    .stat-card .stat-label {
        font-size: 0.875rem !important;
        word-break: break-word !important;
        overflow-wrap: break-word !important;
    }

    /* Form responsive */
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-input, .form-select, .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
    }

    .form-actions {
        flex-direction: column-reverse;
        gap: 0.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
    }

    /* Table responsive */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table {
        min-width: 600px;
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.5rem;
        white-space: nowrap;
    }

    /* Action buttons responsive */
    .action-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .filters {
        width: 100%;
    }

    .filter-form {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-group .form-control {
        width: 100%;
        min-width: auto;
    }

    /* Action buttons in tables */
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn-sm {
        padding: 0.5rem;
        font-size: 0.75rem;
        width: 100%;
    }

    /* Modal responsive */
    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    /* Toast responsive */
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .toast {
        min-width: auto;
        max-width: 100%;
    }

    /* Breadcrumb responsive */
    .breadcrumb {
        font-size: 0.75rem;
        flex-wrap: wrap;
    }

    /* Quick actions responsive */
    .action-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .action-card {
        padding: 1rem;
    }

    /* Info grid responsive */
    .info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Summary responsive */
    .summary-grid {
        max-width: 100%;
        margin: 0;
    }

    /* Product grid responsive */
    .product-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Employee form responsive */
    .employee-form {
        padding: 1rem;
    }

    .form-section {
        gap: 1rem;
    }

    .section-title {
        font-size: 1rem;
    }

    /* Branch form responsive */
    .branch-form {
        padding: 1rem;
    }

    /* User form responsive */
    .user-form {
        padding: 1rem;
    }

    /* Form header responsive */
    .form-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .form-header h1 {
        font-size: 1.25rem;
        margin: 0;
    }

    .form-header p {
        font-size: 0.875rem;
        margin: 0;
    }

    /* Back button responsive */
    .back-button {
        margin-bottom: 1rem;
        width: auto;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Status options responsive */
    .status-options {
        gap: 0.75rem;
    }

    .status-card {
        padding: 0.75rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    /* Branch info responsive */
    .branch-info,
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    /* Password input responsive */
    .password-input-wrapper {
        position: relative;
    }

    .password-toggle {
        right: 0.5rem;
        padding: 0.5rem;
    }

    /* Specific form improvements for mobile */
    .info-section {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .info-section h3 {
        font-size: 1rem;
        margin-bottom: 1rem;
        color: var(--primary);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-section h3 i {
        font-size: 1.25rem;
    }

    /* Form row responsive */
    .form-row {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .form-col {
        flex: 1;
    }

    /* Label improvements */
    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.875rem;
    }

    .form-label.required::after {
        content: ' *';
        color: #ef4444;
    }

    /* Input improvements */
    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 16px; /* Prevent zoom on iOS */
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background: white;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Textarea specific */
    .form-textarea {
        min-height: 100px;
        resize: vertical;
    }

    /* Button improvements for mobile */
    .form-actions {
        display: flex;
        flex-direction: column-reverse;
        gap: 0.75rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .btn-primary {
        background: var(--primary);
        color: white;
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        justify-content: center;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-height: 44px;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        justify-content: center;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-height: 44px;
    }

    .btn-primary:hover,
    .btn-primary:focus {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .btn-secondary:hover,
    .btn-secondary:focus {
        background: #4b5563;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
    }
}

/* Tablet responsive (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .table {
        font-size: 0.9rem;
    }

    .btn {
        padding: 0.75rem 1.25rem;
    }
}

/* Additional Mobile Fixes for Content Overflow */
@media (max-width: 768px) {
    /* Force all containers to respect viewport width */
    * {
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    /* Root level overflow prevention */
    html {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    body {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Specific fixes for dashboard cards */
    .dashboard-container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        overflow-x: hidden !important;
    }

    /* Fix for card content overflow */
    .card, .stat-card, .chart-card, .activity-card, .table-card {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }

    /* Fix for text content that might overflow */
    .stat-value, .stat-label, .card-title, .page-title h1 {
        word-break: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
    }

    /* Fix for long numbers and currency */
    .stat-value {
        font-size: 1.25rem !important;
        line-height: 1.2 !important;
    }

    /* Ensure proper spacing for mobile */
    .container {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }

    /* Mobile header improvements */
    .mobile-header {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        padding: 0 15px !important;
        display: flex !important;
    }

    /* Fix for grid layouts */
    .stats-grid, .action-grid, .info-grid, .product-grid {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure main content doesn't overflow */
    .main-content {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        padding: 60px 10px 10px 10px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
    }

    /* Fix for any wrapper elements */
    .content-wrapper, .page-wrapper {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    /* Prevent any element from causing horizontal scroll */
    div, section, article, main, aside, header, footer {
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
}

/* Large desktop responsive (1025px+) */
@media (min-width: 1025px) {
    .container {
        max-width: 1400px;
        padding: 0 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .form-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px; /* Apple's recommended touch target size */
        padding: 0.75rem 1rem;
        position: relative;
        overflow: hidden;
    }

    .form-input, .form-select, .form-control {
        min-height: 44px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .action-buttons .btn {
        min-height: 40px;
        margin-bottom: 0.25rem;
    }

    /* Increase touch targets */
    .nav-item {
        min-height: 48px;
        padding: 0.75rem 1rem;
        position: relative;
        overflow: hidden;
    }

    .dropdown-item {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    .card {
        position: relative;
        overflow: hidden;
    }

    .action-card {
        position: relative;
        overflow: hidden;
        min-height: 60px;
    }

    .stat-card {
        position: relative;
        overflow: hidden;
        min-height: 80px;
    }

    /* Remove hover effects on touch devices */
    .card:hover,
    .btn:hover,
    .action-card:hover {
        transform: none;
        box-shadow: var(--shadow);
    }

    /* Touch feedback styles */
    .btn:active,
    .nav-item:active,
    .card:active,
    .action-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* Larger touch targets for small elements */
    .btn-sm {
        min-height: 40px;
        padding: 0.5rem 0.75rem;
    }

    .table .btn {
        min-height: 36px;
        padding: 0.375rem 0.75rem;
    }

    /* Form elements touch optimization */
    .form-check-input {
        min-width: 20px;
        min-height: 20px;
    }

    .form-switch .form-check-input {
        min-width: 44px;
        min-height: 24px;
    }

    /* Dropdown touch optimization */
    .dropdown-toggle {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    .dropdown-menu {
        min-width: 200px;
    }

    .dropdown-menu .dropdown-item {
        padding: 0.75rem 1rem;
        font-size: 16px;
    }
}

/* Ripple animation for touch feedback */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.touch-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

/* Enhanced touch feedback */
.touch-feedback {
    position: relative;
    overflow: hidden;
    -webkit-tap-highlight-color: transparent;
}

.touch-feedback::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity 0.15s ease;
    pointer-events: none;
}

.touch-feedback:active::before {
    opacity: 1;
}

/* Improved scrolling on touch devices */
.table-container,
.scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Better focus indicators for touch navigation */
.btn:focus,
.form-input:focus,
.form-select:focus,
.nav-item:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Prevent text selection on touch elements */
.btn,
.nav-item,
.action-card,
.stat-card {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .toast {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .btn {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
}

/* Print styles */
@media print {
    .sidebar,
    .top-bar,
    .action-header,
    .form-actions,
    .toast-container {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }

    .btn {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}
