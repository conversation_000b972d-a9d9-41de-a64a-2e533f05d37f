Scenario ID;Case ID;Test Scenario;Type;Test Case;Pre Condition;Steps Description;Feedback;;
TS.001;TC.001.001;<PERSON><PERSON><PERSON><PERSON> Login Multi-Role;Positif;Login sebagai Direktur dengan kredensial valid;Mengakses halaman login sistem POS;1. Mengaks<PERSON> halaman login sistem POS;Proses login sebagai Direktur sudah berjalan dengan baik, dashboard menampilkan data multi-cabang dengan jelas;;
;;;;;;2. <PERSON><PERSON><PERSON> role "Direktur";;;
;;;;;;3. <PERSON><PERSON>kkan email dan password yang valid;;;
;;;;;;4. <PERSON><PERSON> tombol "Masuk";;;
TS.001;TC.001.002;<PERSON><PERSON><PERSON><PERSON>gin Multi-Role;Positif;Login sebagai Manajer dengan kredensial valid;Mengakses halaman login sistem POS;1. Mengakses halaman login sistem POS;Proses login sebagai Manajer sudah berjalan dengan baik, dashboard menampilkan data cabang spesifik sesuai kebutuhan;;
;;;;;;2. <PERSON><PERSON><PERSON> role "Manajer";;;
;;;;;;3. <PERSON><PERSON><PERSON><PERSON> email dan password yang valid;;;
;;;;;;4. Klik tombol "Masuk";;;
TS.001;TC.001.003;Pengujian Login Multi-Role;Positif;Login sebagai Pegawai dengan kredensial valid;Mengakses halaman login sistem POS;1. Mengakses halaman login sistem POS;Proses login sebagai Pegawai sudah berjalan dengan baik, interface POS mudah digunakan untuk transaksi harian;;
;;;;;;2. Pilih role "Pegawai";;;
;;;;;;3. Masukkan email dan password yang valid;;;
;;;;;;4. Klik tombol "Masuk";;;
TS.001;TC.001.004;Pengujian Login Multi-Role;Negatif;Login dengan kredensial tidak valid;Mengakses halaman login sistem POS;1. Mengakses halaman login sistem POS;Sistem memberikan pesan error yang jelas ketika kredensial salah, membantu user untuk memperbaiki input;;
;;;;;;2. Pilih role apapun;;;
;;;;;;3. Masukkan email atau password yang salah;;;
;;;;;;4. Klik tombol "Masuk";;;
TS.002;TC.002.001;Pengujian Manajemen Produk;Positif;Tambah produk hijab dengan data lengkap;Login sebagai Manajer;1. Akses menu "Kelola Produk";Proses tambah produk sudah sangat membantu untuk mengelola inventory pakaian muslim, form sudah sesuai kebutuhan bisnis;;
;;;;;;2. Klik "Tambah Produk";;;
;;;;;;3. Isi nama produk (contoh: Hijab Segi Empat Premium);;;
;;;;;;4. Pilih kategori "Hijab";;;
;;;;;;5. Isi harga dan stok;;;
;;;;;;6. Klik "Simpan";;;
TS.002;TC.002.002;Pengujian Manajemen Produk;Negatif;Tambah produk dengan SKU yang sudah ada;Login sebagai Manajer;1. Akses menu "Kelola Produk";Sistem memberikan peringatan yang jelas ketika SKU duplikat, membantu menjaga konsistensi data produk;;
;;;;;;2. Klik "Tambah Produk";;;
;;;;;;3. Isi SKU yang sudah ada di database;;;
;;;;;;4. Isi data lainnya;;;
;;;;;;5. Klik "Simpan";;;
TS.002;TC.002.003;Pengujian Upload Gambar Produk;Positif;Upload gambar produk dengan format yang benar;Login sebagai Manajer;1. Akses form tambah/edit produk;Fitur upload gambar sangat dibutuhkan untuk display produk yang menarik, semoga bisa segera diimplementasikan;;
;;;;;;2. Pilih file gambar (JPEG/PNG);;;
;;;;;;3. Upload gambar produk;;;
;;;;;;4. Simpan produk;;;
TS.003;TC.003.001;Pengujian Transaksi Penjualan;Positif;Proses transaksi penjualan gamis dengan pembayaran tunai;Login sebagai Pegawai;1. Akses menu "Transaksi";Interface transaksi sangat user-friendly, memudahkan pegawai untuk melayani pelanggan dengan cepat dan akurat;;
;;;;;;2. Pilih produk "Gamis Syari";;;
;;;;;;3. Masukkan quantity yang diinginkan;;;
;;;;;;4. Isi nama pelanggan;;;
;;;;;;5. Pilih metode pembayaran "Tunai";;;
;;;;;;6. Klik "Proses Transaksi";;;
TS.003;TC.003.002;Pengujian Transaksi dengan Diskon;Positif;Berikan diskon 15% untuk pembelian mukena;Login sebagai Pegawai;1. Proses transaksi normal;Fitur diskon sangat membantu untuk promosi dan customer retention, perhitungan otomatis memudahkan pegawai;;
;;;;;;2. Pilih produk "Mukena Katun";;;
;;;;;;3. Berikan diskon 15%;;;
;;;;;;4. Verifikasi total harga;;;
;;;;;;5. Proses pembayaran;;;
TS.003;TC.003.003;Pengujian Validasi Stok;Negatif;Coba jual produk dengan quantity melebihi stok;Login sebagai Pegawai;1. Pilih produk dengan stok terbatas;Validasi stok berfungsi dengan baik, mencegah overselling dan menjaga akurasi inventory;;
;;;;;;2. Masukkan quantity > stok tersedia;;;
;;;;;;3. Coba proses transaksi;;;
TS.003;TC.003.004;Pengujian Metode Pembayaran;Positif;Transaksi dengan pembayaran QRIS;Login sebagai Pegawai;1. Proses transaksi normal;Variasi metode pembayaran sudah sesuai dengan kebutuhan pelanggan modern, terutama untuk pembayaran digital;;
;;;;;;2. Pilih metode pembayaran "QRIS";;;
;;;;;;3. Konfirmasi pembayaran;;;
;;;;;;4. Cetak struk;;;
TS.004;TC.004.001;Pengujian Manajemen Retur;Positif;Proses retur hijab dengan kondisi baik;Login sebagai Manajer;1. Akses menu "Retur";Proses retur sudah terstruktur dengan baik, approval workflow membantu kontrol kualitas dan customer service;;
;;;;;;2. Pilih transaksi yang akan diretur;;;
;;;;;;3. Pilih produk hijab yang diretur;;;
;;;;;;4. Isi alasan retur;;;
;;;;;;5. Pilih kondisi "Baik";;;
;;;;;;6. Approve retur;;;
TS.004;TC.004.002;Pengujian Retur Barang Rusak;Positif;Proses retur gamis dengan kondisi rusak;Login sebagai Manajer;1. Akses menu "Retur";Sistem damaged stock sangat membantu untuk tracking barang rusak dan mencegah barang rusak kembali ke stok normal;;
;;;;;;2. Pilih transaksi yang akan diretur;;;
;;;;;;3. Pilih produk gamis yang diretur;;;
;;;;;;4. Isi alasan retur;;;
;;;;;;5. Pilih kondisi "Rusak";;;
;;;;;;6. Approve retur;;;
TS.005;TC.005.001;Pengujian Manajemen Cabang;Positif;Tambah cabang baru di area operasional Jakarta;Login sebagai Direktur;1. Akses menu "Kelola Cabang";Fitur multi-cabang sangat membantu untuk ekspansi bisnis, form sudah lengkap dengan area operasional yang jelas;;
;;;;;;2. Klik "Tambah Cabang";;;
;;;;;;3. Isi nama cabang;;;
;;;;;;4. Isi alamat lengkap;;;
;;;;;;5. Isi area operasional "Jakarta";;;
;;;;;;6. Isi kontak cabang;;;
;;;;;;7. Klik "Simpan";;;
TS.005;TC.005.002;Pengujian Validasi Data Cabang;Negatif;Tambah cabang dengan nomor telepon tidak valid;Login sebagai Direktur;1. Akses form tambah cabang;Validasi nomor telepon perlu diperbaiki, sebaiknya hanya menerima format angka dan dibatasi maksimal 12 digit;;
;;;;;;2. Isi nomor telepon dengan huruf;;;
;;;;;;3. Isi data lainnya dengan benar;;;
;;;;;;4. Coba simpan cabang;;;
TS.006;TC.006.001;Pengujian Manajemen Pengguna;Positif;Tambah pegawai baru untuk cabang tertentu;Login sebagai Direktur;1. Akses menu "Kelola Pengguna";Sistem manajemen user sudah baik, role-based access membantu mengatur kewenangan setiap level pengguna;;
;;;;;;2. Klik "Tambah Pengguna";;;
;;;;;;3. Isi data pegawai baru;;;
;;;;;;4. Pilih role "Pegawai";;;
;;;;;;5. Assign ke cabang tertentu;;;
;;;;;;6. Klik "Simpan";;;
TS.006;TC.006.002;Pengujian Validasi Password;Negatif;Tambah user dengan password terlalu pendek;Login sebagai Direktur;1. Akses form tambah pengguna;Validasi password sudah baik dengan minimum 8 karakter, membantu menjaga keamanan sistem;;
;;;;;;2. Isi password kurang dari 8 karakter;;;
;;;;;;3. Isi data lainnya dengan benar;;;
;;;;;;4. Coba simpan pengguna;;;
TS.007;TC.007.001;Pengujian Laporan Terintegrasi;Positif;Generate laporan penjualan harian;Login sebagai Direktur;1. Akses menu "Laporan";Laporan terintegrasi sangat membantu untuk analisis bisnis, data akurat dan format mudah dipahami;;
;;;;;;2. Pilih "Laporan Penjualan";;;
;;;;;;3. Pilih periode "Harian";;;
;;;;;;4. Pilih tanggal hari ini;;;
;;;;;;5. Klik "Generate Laporan";;;
TS.007;TC.007.002;Pengujian Export Laporan;Positif;Export laporan ke format Excel;Login sebagai Direktur;1. Generate laporan apapun;Fitur export ke Excel sangat membantu untuk analisis lebih lanjut dan presentasi ke stakeholder;;
;;;;;;2. Klik "Export ke Excel";;;
;;;;;;3. Download file Excel;;;
TS.008;TC.008.001;Pengujian Dashboard Multi-Role;Positif;Akses dashboard sebagai Direktur;Login sebagai Direktur;1. Login berhasil;Dashboard direktur memberikan overview yang komprehensif untuk monitoring performa multi-cabang;;
;;;;;;2. Verifikasi konten dashboard;;;
;;;;;;3. Cek data multi-cabang;;;
TS.008;TC.008.002;Pengujian Dashboard Multi-Role;Positif;Akses dashboard sebagai Manajer;Login sebagai Manajer;1. Login berhasil;Dashboard manajer fokus pada data cabang spesifik, sangat relevan untuk operasional harian;;
;;;;;;2. Verifikasi konten dashboard;;;
;;;;;;3. Cek data cabang spesifik;;;
TS.008;TC.008.003;Pengujian Dashboard Multi-Role;Positif;Akses dashboard sebagai Pegawai;Login sebagai Pegawai;1. Login berhasil;Dashboard pegawai simple dan fokus pada tugas harian, tidak membingungkan dengan data yang tidak relevan;;
;;;;;;2. Verifikasi konten dashboard;;;
;;;;;;3. Cek data personal performance;;;
TS.009;TC.009.001;Pengujian Kategori Produk;Positif;Tambah kategori baru untuk aksesoris muslim;Login sebagai Manajer;1. Akses menu "Kelola Kategori";Manajemen kategori membantu organisasi produk pakaian muslim, auto-generate slug sangat praktis;;
;;;;;;2. Klik "Tambah Kategori";;;
;;;;;;3. Isi nama kategori "Aksesoris Muslim";;;
;;;;;;4. Isi deskripsi kategori;;;
;;;;;;5. Klik "Simpan";;;
TS.009;TC.009.002;Pengujian Validasi Kategori;Negatif;Tambah kategori dengan nama yang sudah ada;Login sebagai Manajer;1. Akses form tambah kategori;Validasi duplikasi kategori berfungsi baik, mencegah kebingungan dalam pengelolaan produk;;
;;;;;;2. Isi nama kategori yang sudah ada;;;
;;;;;;3. Isi deskripsi;;;
;;;;;;4. Coba simpan kategori;;;
TS.010;TC.010.001;Pengujian Responsivitas Mobile;Positif;Akses sistem via tablet untuk transaksi;Menggunakan tablet;1. Buka browser di tablet;Interface responsive sangat membantu untuk penggunaan di toko dengan space terbatas, touch-friendly;;
;;;;;;2. Login sebagai Pegawai;;;
;;;;;;3. Proses transaksi normal;;;
;;;;;;4. Verifikasi kemudahan penggunaan;;;
TS.010;TC.010.002;Pengujian Mobile Interface;Positif;Navigasi menu di smartphone;Menggunakan smartphone;1. Akses sistem via smartphone;Menu navigation di mobile sudah intuitif, memudahkan akses fitur utama;;
;;;;;;2. Login dengan role apapun;;;
;;;;;;3. Navigasi berbagai menu;;;
;;;;;;4. Test responsivitas interface;;;
TS.011;TC.011.001;Pengujian Stok Management;Positif;Update stok produk hijab;Login sebagai Manajer;1. Akses menu "Kelola Produk";Fitur update stok sangat praktis, real-time update membantu akurasi inventory;;
;;;;;;2. Pilih produk hijab;;;
;;;;;;3. Klik "Edit Stok";;;
;;;;;;4. Update jumlah stok;;;
;;;;;;5. Simpan perubahan;;;
TS.011;TC.011.002;Pengujian Alert Stok Rendah;Positif;Verifikasi alert untuk produk dengan stok ≤ 10;Login sebagai Manajer;1. Akses dashboard manajer;Alert stok rendah sangat membantu untuk reorder planning, mencegah kehabisan stok produk populer;;
;;;;;;2. Cek notifikasi stok rendah;;;
;;;;;;3. Verifikasi produk dengan stok ≤ 10;;;
TS.012;TC.012.001;Pengujian Search & Filter;Positif;Pencarian produk berdasarkan nama;Login sebagai Pegawai;1. Akses halaman produk;Fitur search sangat membantu untuk menemukan produk dengan cepat saat melayani pelanggan;;
;;;;;;2. Ketik nama produk di search box;;;
;;;;;;3. Verifikasi hasil pencarian;;;
TS.012;TC.012.002;Pengujian Filter Produk;Positif;Filter produk berdasarkan kategori hijab;Login sebagai Manajer;1. Akses halaman kelola produk;Filter kategori memudahkan pengelolaan produk berdasarkan jenis pakaian muslim;;
;;;;;;2. Pilih filter kategori "Hijab";;;
;;;;;;3. Verifikasi hasil filter;;;
TS.013;TC.013.001;Pengujian Invoice Generation;Positif;Generate dan cetak struk transaksi;Login sebagai Pegawai;1. Selesaikan transaksi normal;Format struk sudah profesional dan informatif, nomor invoice unik membantu tracking;;
;;;;;;2. Verifikasi nomor invoice unik;;;
;;;;;;3. Cetak struk;;;
;;;;;;4. Verifikasi format dan konten struk;;;
TS.013;TC.013.002;Pengujian Cetak Ulang Struk;Positif;Cetak ulang struk transaksi sebelumnya;Login sebagai Pegawai;1. Akses riwayat transaksi;Fitur cetak ulang sangat membantu ketika pelanggan kehilangan struk atau butuh duplikat;;
;;;;;;2. Pilih transaksi yang sudah selesai;;;
;;;;;;3. Klik "Cetak Ulang Struk";;;
;;;;;;4. Verifikasi struk yang dicetak;;;
TS.014;TC.014.001;Pengujian Keamanan Akses;Negatif;Pegawai coba akses menu Direktur;Login sebagai Pegawai;1. Coba akses URL dashboard direktur;Sistem keamanan role-based access berfungsi dengan baik, mencegah akses tidak authorized;;
TS.014;TC.014.002;Pengujian Session Management;Positif;Logout dari sistem;Login dengan role apapun;1. Klik menu "Logout";Proses logout bersih dan aman, session management berfungsi dengan baik;;
;;;;;;2. Konfirmasi logout;;;
;;;;;;3. Verifikasi redirect ke login;;;
TS.015;TC.015.001;Pengujian Damaged Stock;Positif;Kelola barang rusak dari retur;Login sebagai Manajer;1. Proses retur dengan kondisi "Rusak";Sistem damaged stock membantu tracking dan disposal barang rusak, mencegah kerugian lebih lanjut;;
;;;;;;2. Verifikasi masuk ke damaged stock;;;
;;;;;;3. Akses menu "Damaged Stock";;;
;;;;;;4. Kelola action untuk barang rusak;;;
TS.016;TC.016.001;Pengujian Performance;Positif;Load halaman dengan banyak data produk;Login sebagai Manajer;1. Akses halaman produk dengan 500+ items;Performance sistem cukup baik untuk data dalam jumlah normal, loading time masih acceptable;;
;;;;;;2. Verifikasi loading time;;;
;;;;;;3. Test scroll dan pagination;;;
TS.017;TC.017.001;Pengujian Data Integrity;Positif;Konsistensi stok setelah transaksi dan retur;Login sebagai Pegawai dan Manajer;1. Catat stok awal produk;Integritas data terjaga dengan baik, stok selalu akurat setelah transaksi dan retur;;
;;;;;;2. Proses transaksi;;;
;;;;;;3. Proses retur;;;
;;;;;;4. Verifikasi konsistensi stok;;;
TS.018;TC.018.001;Pengujian User Experience;Positif;Workflow lengkap dari login hingga logout;Akses sistem sebagai user baru;1. Login pertama kali;Overall user experience sangat baik, workflow intuitif dan sesuai dengan kebutuhan bisnis retail pakaian muslim;;
;;;;;;2. Eksplorasi semua fitur;;;
;;;;;;3. Lakukan aktivitas normal;;;
;;;;;;4. Logout dari sistem;;;
