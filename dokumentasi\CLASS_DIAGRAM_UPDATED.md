# Class Diagram Updated - Sistem POS UMKM
## Class Diagram dengan Penambahan Fitur Kelola Akun

### 📋 **Ringkasan**
Class Diagram yang telah diperbarui untuk mendukung Activity Diagram "Ke<PERSON>la Akun" dengan penambahan attributes, operations, dan class baru untuk audit trail.

---

## 🎨 **Class Diagram - Mermaid Format**

```mermaid
classDiagram
    class users {
        -id: bigint
        -email: varchar
        -password: varchar
        -role: enum
        -phone: varchar
        -address: text
        -profile_picture: varchar
        -updated_by: bigint
        -last_login_at: datetime
        +login(): varchar
        +loadDashboard(): void
        +updateProfile(): boolean
        +validateProfileData(): boolean
        +canManageUser(): boolean
        +activateUser(): void
        +deactivateUser(): void
        +uploadProfilePicture(): varchar
        +createUser(): boolean
        +updateUser(): boolean
        +deleteUser(): boolean
        +getUserById(): users
        +getAllUsers(): collection
        +searchUsers(): collection
    }

    class user_logs {
        -id: bigint
        -user_id: bigint
        -updated_by: bigint
        -action_type: enum
        -field_changed: varchar
        -old_value: text
        -new_value: text
        -ip_address: varchar
        -created_at: datetime
        +logUserAction(): void
        +getUserLogs(): collection
    }

    class categories {
        -id: bigint
        -name: varchar
        +getProductsCount(): int
        +generateSlug(): void
    }

    class products {
        -id: bigint
        -name: varchar
        -stock: int
        -base_price: decimal
        +updateStock(): void
    }

    class transactions {
        -id: bigint
        -invoice_number: varchar
        -subtotal: decimal
        -total_amount: decimal
        -status: enum
        +calculateTotal(): decimal
        +saveTransaction(): void
    }

    class transaction_items {
        -id: bigint
        -quantity: int
        -price: decimal
        +calculateSubtotal(): decimal
    }

    class return_transactions {
        -id: bigint
        -return_number: varchar
        -total: decimal
        -status: enum
        +approve(): boolean
        +reject(): boolean
    }

    class return_items {
        -id: bigint
        -quantity: int
        -condition: enum
        +calculateSubtotal(): void
        +isGoodCondition(): boolean
        +isDamaged(): boolean
    }

    class damaged_stocks {
        -id: bigint
        -quantity: int
        -condition: enum
        +markAsDisposed(): void
    }

    users --> user_logs
    categories --> products
    products --> transaction_items
    transactions --> transaction_items
    users --> transactions
    transactions --> return_transactions
    return_transactions --> return_items
    return_items --> damaged_stocks
    users --> return_transactions
    products --> return_items
    products --> damaged_stocks
```

---

## � **Penjelasan Perubahan**

### **1. Class `users` - Penambahan Attributes:**
- `phone: varchar` - Nomor telepon user
- `address: text` - Alamat lengkap user  
- `profile_picture: varchar` - Path foto profil
- `updated_by: bigint` - ID user yang melakukan update
- `last_login_at: datetime` - Timestamp login terakhir

### **2. Class `users` - Penambahan Operations:**
- `updateProfile(data): boolean` - Update profil user
- `validateProfileData(data): boolean` - Validasi data profil
- `canManageUser(userId): boolean` - Cek permission manage user
- `activateUser(): void` - Aktivasi akun user
- `deactivateUser(): void` - Deaktivasi akun user
- `uploadProfilePicture(file): varchar` - Upload foto profil
- `createUser(data): boolean` - Buat user baru
- `updateUser(id, data): boolean` - Update data user
- `deleteUser(id): boolean` - Hapus user (soft delete)
- `getUserById(id): users` - Ambil user berdasarkan ID
- `getAllUsers(): collection` - Ambil semua user
- `searchUsers(keyword): collection` - Cari user berdasarkan keyword

### **3. Class Baru: `user_logs`**
**Purpose**: Audit trail untuk tracking semua perubahan pada user accounts

**Attributes:**
- `id: bigint` - Primary key
- `user_id: bigint` - ID user yang diubah
- `updated_by: bigint` - ID user yang melakukan perubahan
- `action_type: enum` - Jenis aksi (create, update, delete, activate, deactivate)
- `field_changed: varchar` - Field yang diubah
- `old_value: text` - Nilai lama
- `new_value: text` - Nilai baru
- `ip_address: varchar` - IP address yang melakukan perubahan
- `created_at: datetime` - Timestamp perubahan

**Operations:**
- `logUserAction(action, userId, updatedBy): void` - Log aksi user
- `getUserLogs(userId): collection` - Ambil log berdasarkan user

### **4. Relationships dBndn**
- `users` **1** ←→ **\*** `user_logs` (user_id) - *ser memiliki banyak log - `users` **1** ←→ **\*** `user_logs` (updated_by) *MUs`-obanemo- tje p_tusbanyak - at bit y
---

## 🎯 **Mapping ke Activity Diagram "Kelola Akun"**

### **Pilih Aksi:**
- **Tambah Akun** → `createUser(data): boolean`
- **Edit Akun** → `updateUser(id, data): boolean` + `updateProfile(data): boolean`
- **Hapus Akun** → `deleteUser(id): boolean`

### **Update Data Account:**
- **Validation** → `validateProfileData(data): boolean`
- **Authorization** → `canManageUser(userId): boolean`
- **Audit Trail** → `logUserAction()` otomatis ter-trigger
- **Profile Picture** → `uploadProfilePicture(file): varchar`

### **Business Logic Support:**
- **Role-based Access** → `canManageUser()` cek permission berdasarkan role
- **Data Integrity** → Validation methods untuk memastikan data valid
- **Audit Compliance** → `user_logs` untuk tracking semua perubahan
- **Soft Delete** → `deleteUser()` tidak menghapus permanent, hanya marking

---

## 🔗 **Integration dengan Existing System**

### **Backward Compatibility:**
- Semua existing relationships tetap utuh
- Existing operations tidak berubah
- Penambahan bersifat additive, tidak breaking changes

### **Forward Compatibility:**
- Struktur mendukung Sequence Diagram untuk "Kelola Akun"
- Audit trail siap untuk compliance requirements
- Extensible untuk fitur user management lainnya

### **Database Impact:**
- Tambah kolom baru di table `users`
- Buat table baru `user_logs`
- Tambah foreign key constraints untuk audit trail

Class Diagram ini siap untuk implementasi Activity "Kelola Akun" dan Sequence Diagram selanjutnya! 🚀
