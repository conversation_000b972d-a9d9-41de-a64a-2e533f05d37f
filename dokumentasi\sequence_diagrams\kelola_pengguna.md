# Sequence Diagram - <PERSON><PERSON>la Pengguna
## Sistem POS UMKM - Fitur Kelola Pengguna

### 📋 **<PERSON>kasan**
Sequence Diagram untuk fitur "<PERSON><PERSON>la Pengguna" yang menunjukkan interaksi antara Direktur/Manajer dengan sistem untuk mengelola akun pengguna (tambah, edit, hapus).

---

## 🎨 **Sequence Diagram - Mermaid Format**

```mermaid
sequenceDiagram
    participant DM as Direktur/Manajer
    participant UI as Ke<PERSON>la_User_Page
    participant UC as UserController
    participant DB as Database

    Note over DM, DB: Akses <PERSON>aman Kelola Pengguna
    DM->>+UI: pilihKelolaUser()
    UI->>+UC: index()
    UC->>+DB: getAllUsers()
    DB-->>-UC: userData
    UC-->>-UI: displayUserList()
    UI-->>-DM: tampilkanDaftarUser

    Note over DM, DB: Tambah Akun Baru
    DM->>+UI: pilihTambahAkun()
    UI->>+UC: create()
    UC-->>-UI: showCreateForm()
    UI-->>-DM: tampilkanFormTambah

    DM->>+UI: inputDataUser()
    UI->>+UC: store()
    UC->>+DB: createUser()
    DB-->>-UC: userCreated
    UC-->>-UI: redirectSuccess()
    UI-->>-DM: pesanSukses

    Note over DM, DB: Edit Akun
    DM->>+UI: pilihEditAkun()
    UI->>+UC: edit()
    UC->>+DB: getUserById()
    DB-->>-UC: userData
    UC-->>-UI: showEditForm()
    UI-->>-DM: tampilkanFormEdit

    DM->>+UI: updateDataUser()
    UI->>+UC: update()
    UC->>+DB: updateUser()
    DB-->>-UC: userUpdated
    UC-->>-UI: redirectSuccess()
    UI-->>-DM: pesanSukses

    Note over DM, DB: Hapus Akun
    DM->>+UI: pilihHapusAkun()
    UI->>+UC: destroy()
    UC->>+DB: deleteUser()
    DB-->>-UC: userDeleted
    UC-->>-UI: redirectSuccess()
    UI-->>-DM: pesanSukses
```

---

## 📊 **Penjelasan Sequence Flow**

### **1. Akses Halaman Kelola Pengguna (Steps 1-8):**
- **Direktur/Manajer** mengakses halaman kelola pengguna
- **UI** memanggil **UserController.index()**
- **Controller** mengambil semua data users dari **User Model**
- **Model** query ke **Database** untuk mendapatkan data users
- **UI** menampilkan halaman dengan daftar users

### **2. Tambah Akun (Steps 9-28):**
- **Direktur/Manajer** memilih tambah akun
- **UI** menampilkan form tambah user
- **User** mengisi data dan submit
- **Controller** melakukan validasi data
- Jika valid: create user baru + log aktivitas
- Jika tidak valid: tampilkan error message

### **3. Edit Akun (Steps 29-52):**
- **Direktur/Manajer** memilih edit akun tertentu
- **Controller** mengambil data user berdasarkan ID
- **UI** menampilkan form edit dengan data existing
- **User** update data dan submit
- **Controller** melakukan validasi dan update
- **System** mencatat log perubahan

### **4. Hapus Akun (Steps 53-68):**
- **Direktur/Manajer** memilih hapus akun
- **Controller** cek permission untuk hapus user
- Jika diizinkan: soft delete user + log aktivitas
- Jika tidak diizinkan: tampilkan error message

---

## 🎯 **Mapping ke Activity Diagram**

### **Activity "Pilih Aksi":**
- **Tambah Akun** → Steps 9-28 (Create flow)
- **Edit Akun** → Steps 29-52 (Update flow)  
- **Hapus Akun** → Steps 53-68 (Delete flow)

### **Activity "Update Data Account":**
- **Validation** → Steps 15-16, 39-40 (validateProfileData)
- **Database Update** → Steps 18-19, 42-43 (INSERT/UPDATE)
- **Audit Trail** → Steps 21-24, 45-48, 61-64 (logUserAction)

---

## 🔧 **Technical Implementation Details**

### **Objects & Responsibilities:**
- **UI (Web Interface)**: Menampilkan form dan handle user interaction
- **UserController**: Business logic dan orchestration
- **User Model**: Data manipulation dan validation
- **UserLog Model**: Audit trail management
- **Database**: Data persistence

### **Key Methods Used:**
- `getAllUsers()` - Ambil semua data users
- `validateProfileData()` - Validasi input data
- `createUser()` - Buat user baru
- `updateUser()` - Update data user
- `deleteUser()` - Soft delete user
- `logUserAction()` - Catat aktivitas ke audit trail

### **Security Features:**
- **Permission Check**: `canManageUser()` sebelum delete
- **Audit Trail**: Semua aktivitas tercatat di `user_logs`
- **Soft Delete**: User tidak dihapus permanent
- **Validation**: Input data divalidasi sebelum disimpan

Sequence Diagram ini mengikuti pola MVC Laravel dan siap untuk implementasi! 🚀
