<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ForceHttps
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Force HTTPS untuk ngrok dan production
        if (!$request->secure() && 
            (app()->environment('production') || 
             str_contains(config('app.url'), 'ngrok') ||
             $request->header('x-forwarded-proto') === 'https')) {
            
            return redirect()->secure($request->getRequestUri());
        }

        return $next($request);
    }
}
