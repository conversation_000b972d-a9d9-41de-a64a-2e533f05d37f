@startuml sequence_diagram_transaksi
!theme plain
title Sequence Diagram - Proses <PERSON>aks<PERSON> (Simplified)

' Styling sesuai referensi
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowColor #333333
skinparam sequenceActorBorderColor #333333
skinparam sequenceActorBackgroundColor #E3F2FD
skinparam sequenceParticipantBorderColor #333333
skinparam sequenceParticipantBackgroundColor #F3E5F5
skinparam sequenceGroupBackgroundColor #F8F9FA
skinparam sequenceLifeLineBorderColor #6C757D
skinparam sequenceLifeLineBackgroundColor #FFFFFF

' Actors dan Participants (1 actor + 3 objects)
actor "👤 Pegawai" as Pegawai
participant "🖥️ UI" as UI
participant "🌐 Controller" as Controller
participant "🗄️ Database" as Database

' Sequence Flow
Pegawai -> UI: 1. <PERSON><PERSON>
activate UI
UI -> Controller: :order(POST /transactions/create)
activate Controller
Controller -> Database: :order(SELECT products, stock)
activate Database
Database --> Controller: :order(product data)
deactivate Database
Controller --> UI: :order(transaction form)
deactivate Controller
UI --> Pegawai: :order(tampilkan form transaksi)
deactivate UI

Pegawai -> UI: 2. Input Data Produk
activate UI
UI -> Controller: :order(POST /transactions/add-item)
activate Controller
Controller -> Database: :order(UPDATE stock, INSERT transaction_items)
activate Database
Database --> Controller: :order(item added)
deactivate Database
Controller --> UI: :order(updated cart)
deactivate Controller
UI --> Pegawai: :order(tampilkan keranjang)
deactivate UI

Pegawai -> UI: 3. Proses Pembayaran
activate UI
UI -> Controller: :order(POST /transactions/process)
activate Controller
Controller -> Database: :order(INSERT transaction, UPDATE stock)
activate Database
Database --> Controller: :order(transaction saved)
deactivate Database
Controller --> UI: :order(redirect to invoice)
deactivate Controller
UI --> Pegawai: :order(tampilkan struk)
deactivate UI

note right of Pegawai
  **Implementasi Laravel:**
  - TransactionController
  - Transaction Model
  - Product Model
  - Blade Templates
end note

@enduml
