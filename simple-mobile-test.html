<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Mobile Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f0f9ff;
            overflow-x: hidden;
        }

        /* Mobile Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            padding: 15px;
            cursor: pointer;
        }

        .mobile-title {
            font-size: 20px;
            font-weight: 700;
        }

        /* Simple Mobile Sidebar */
        .mobile-sidebar {
            position: fixed;
            top: 0;
            left: -100%;
            width: 80%;
            max-width: 300px;
            height: 100vh;
            background: #1e40af;
            z-index: 9999;
            transition: left 0.3s ease;
            overflow-y: auto;
            color: white;
        }

        .mobile-sidebar.show {
            left: 0;
        }

        .mobile-sidebar-header {
            padding: 30px 20px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            background: rgba(0,0,0,0.1);
        }

        .mobile-sidebar-header h2 {
            color: white;
            font-size: 24px;
            margin: 0 0 10px 0;
        }

        .mobile-nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .mobile-nav-item {
            display: block;
            padding: 20px 25px;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 16px;
            transition: background 0.2s ease;
        }

        .mobile-nav-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .mobile-nav-item i {
            display: inline-block;
            width: 25px;
            margin-right: 15px;
            color: white;
            font-size: 16px;
        }

        /* Mobile Overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            display: none;
        }

        .mobile-overlay.show {
            display: block;
        }

        /* Main Content */
        .main-content {
            padding: 80px 20px 20px 20px;
            min-height: 100vh;
        }

        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }

        .debug-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 24px;
            cursor: pointer;
            z-index: 99999;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        #status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">SIMPLE TEST</h1>
        <div style="width: 32px;"></div>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Mobile Sidebar -->
    <div class="mobile-sidebar">
        <div class="mobile-sidebar-header">
            <h2>CATAT</h2>
            <p style="color: rgba(255,255,255,0.8); margin: 5px 0 0 0;">Simple Test</p>
        </div>
        <div class="mobile-nav-list">
            <a href="#" class="mobile-nav-item" onclick="logClick('Dashboard')">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <a href="#" class="mobile-nav-item" onclick="logClick('Pegawai')">
                <i class="fas fa-users"></i>
                Pegawai
            </a>
            <a href="#" class="mobile-nav-item" onclick="logClick('Cabang')">
                <i class="fas fa-store"></i>
                Cabang
            </a>
            <a href="#" class="mobile-nav-item" onclick="logClick('Produk')">
                <i class="fas fa-box"></i>
                Produk
            </a>
            <a href="#" class="mobile-nav-item" onclick="logClick('Laporan')">
                <i class="fas fa-chart-line"></i>
                Laporan
            </a>
            <a href="#" class="mobile-nav-item" onclick="logClick('Logout')">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="test-card">
            <h2>Simple Mobile Sidebar Test</h2>
            <p>Test basic mobile sidebar functionality.</p>
            <button class="test-btn" onclick="toggleMobileMenu()">Toggle Sidebar</button>
            <button class="test-btn" onclick="testManual()">Manual Test</button>
        </div>

        <div class="test-card">
            <h3>Status Log</h3>
            <div id="status">Ready for testing...</div>
        </div>
    </main>

    <!-- Debug Button -->
    <button class="debug-btn" onclick="debugToggle()">🔧</button>

    <script>
        let logs = [];

        function toggleMobileMenu() {
            log('🔄 Toggle called');
            const mobileSidebar = document.querySelector('.mobile-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (!mobileSidebar) {
                log('❌ Mobile sidebar not found');
                return;
            }

            const isOpen = mobileSidebar.classList.contains('show');
            log(`Current state: ${isOpen ? 'OPEN' : 'CLOSED'}`);
            
            if (isOpen) {
                log('🔒 Closing sidebar');
                mobileSidebar.classList.remove('show');
                mobileSidebar.style.left = '-100%';
                if (overlay) {
                    overlay.classList.remove('show');
                    overlay.style.display = 'none';
                }
                if (menuBtn) menuBtn.className = 'fas fa-bars';
            } else {
                log('🔓 Opening sidebar');
                mobileSidebar.classList.add('show');
                mobileSidebar.style.left = '0';
                if (overlay) {
                    overlay.classList.add('show');
                    overlay.style.display = 'block';
                }
                if (menuBtn) menuBtn.className = 'fas fa-times';
            }
        }

        function closeMobileMenu() {
            log('🔒 Close via overlay');
            const mobileSidebar = document.querySelector('.mobile-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (mobileSidebar) {
                mobileSidebar.classList.remove('show');
                mobileSidebar.style.left = '-100%';
            }
            if (overlay) {
                overlay.classList.remove('show');
                overlay.style.display = 'none';
            }
            if (menuBtn) menuBtn.className = 'fas fa-bars';
        }

        function testManual() {
            log('🔧 Manual test');
            const sidebar = document.querySelector('.mobile-sidebar');
            if (sidebar) {
                const isVisible = sidebar.style.left === '0px';
                sidebar.style.left = isVisible ? '-100%' : '0px';
                log(`Manual: ${isVisible ? 'CLOSED' : 'OPENED'}`);
            }
        }

        function debugToggle() {
            log('🚨 Debug toggle');
            toggleMobileMenu();
        }

        function logClick(item) {
            log(`📱 Clicked: ${item}`);
            closeMobileMenu();
        }

        function log(message) {
            const time = new Date().toLocaleTimeString();
            const logEntry = `${time}: ${message}`;
            logs.push(logEntry);
            console.log(logEntry);
            
            const status = document.getElementById('status');
            status.textContent = logs.slice(-8).join('\n');
            status.scrollTop = status.scrollHeight;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Simple test page loaded');
            log(`Screen: ${window.innerWidth}x${window.innerHeight}`);
            log(`User Agent: ${navigator.userAgent.substring(0, 50)}...`);
        });
    </script>
</body>
</html>
