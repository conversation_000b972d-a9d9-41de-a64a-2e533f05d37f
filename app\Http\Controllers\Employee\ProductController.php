<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'branch']);
        
        // Filter by user's branch if employee
        if (auth()->user()->isEmployee()) {
            $query->where('branch_id', auth()->user()->branch_id);
        }
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $products = $query->paginate(12);
        $categories = Category::all();
        
        return view('employee.products.index', compact('products', 'categories'));
    }
    
    public function show(Product $product)
    {
        // Check if employee can view this product (same branch)
        if (auth()->user()->isEmployee() && $product->branch_id !== auth()->user()->branch_id) {
            abort(403, 'Unauthorized access to this product.');
        }
        
        $product->load(['category', 'branch']);
        
        return view('employee.products.show', compact('product'));
    }
}
