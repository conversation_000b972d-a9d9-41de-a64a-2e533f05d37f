<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Sidebar Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f0f9ff;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
        }

        .menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            padding: 10px;
            cursor: pointer;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: -100%;
            width: 280px;
            height: 100vh;
            background: #1e40af;
            z-index: 99999;
            transition: left 0.3s ease;
            color: white;
            overflow-y: auto;
        }

        .sidebar.show {
            left: 0;
        }

        .sidebar-header {
            padding: 30px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .sidebar-header h2 {
            color: white;
            margin: 0;
        }

        .nav-item {
            display: block;
            padding: 20px 25px;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }

        /* Overlay */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99998;
            display: none;
        }

        .overlay.show {
            display: block;
        }

        /* Content */
        .content {
            padding: 80px 20px 20px 20px;
            min-height: 100vh;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .debug-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 999999;
        }

        .debug-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .debug-btn.red { background: #ff4444; }
        .debug-btn.green { background: #00aa00; }
        .debug-btn.blue { background: #0066cc; }

        #log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <button class="menu-btn" onclick="toggle()">☰</button>
        <h1>MINIMAL TEST</h1>
        <div></div>
    </div>

    <!-- Overlay -->
    <div class="overlay" onclick="close()"></div>

    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>MENU</h2>
        </div>
        <a href="#" class="nav-item" onclick="log('Dashboard clicked')">🏠 Dashboard</a>
        <a href="#" class="nav-item" onclick="log('Products clicked')">📦 Products</a>
        <a href="#" class="nav-item" onclick="log('Reports clicked')">📊 Reports</a>
        <a href="#" class="nav-item" onclick="log('Settings clicked')">⚙️ Settings</a>
        <a href="#" class="nav-item" onclick="log('Logout clicked')">🚪 Logout</a>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="card">
            <h2>Minimal Sidebar Test</h2>
            <p>Test basic sidebar functionality with minimal code.</p>
            <button class="btn" onclick="toggle()">Toggle Sidebar</button>
            <button class="btn" onclick="forceShow()">Force Show</button>
            <button class="btn" onclick="analyze()">Analyze</button>
        </div>

        <div class="card">
            <h3>Log</h3>
            <div id="log">Ready...</div>
        </div>
    </div>

    <!-- Debug Buttons -->
    <div class="debug-buttons">
        <button class="debug-btn red" onclick="toggle()" title="Toggle">☰</button>
        <button class="debug-btn green" onclick="forceShow()" title="Force Show">👁️</button>
        <button class="debug-btn blue" onclick="analyze()" title="Analyze">ℹ️</button>
    </div>

    <script>
        let logs = [];

        function toggle() {
            log('🔄 Toggle called');
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.overlay');

            if (!sidebar) {
                log('❌ Sidebar not found');
                return;
            }

            const isOpen = sidebar.style.left === '0px' || sidebar.classList.contains('show');
            log(`Current state: ${isOpen ? 'OPEN' : 'CLOSED'}`);

            if (isOpen) {
                log('🔒 Closing');
                sidebar.style.left = '-100%';
                sidebar.classList.remove('show');
                overlay.style.display = 'none';
                overlay.classList.remove('show');
            } else {
                log('🔓 Opening');
                sidebar.style.left = '0px';
                sidebar.classList.add('show');
                overlay.style.display = 'block';
                overlay.classList.add('show');
            }
        }

        function close() {
            log('🔒 Close via overlay');
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.overlay');

            if (sidebar) {
                sidebar.style.left = '-100%';
                sidebar.classList.remove('show');
            }
            if (overlay) {
                overlay.style.display = 'none';
                overlay.classList.remove('show');
            }
        }

        function forceShow() {
            log('👁️ Force show');
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.style.left = '0px';
                sidebar.style.display = 'block';
                sidebar.style.visibility = 'visible';
                sidebar.style.opacity = '1';
                sidebar.classList.add('show');
                log('✅ Sidebar forced visible');
            } else {
                log('❌ Sidebar not found');
            }
        }

        function analyze() {
            log('🔍 Analyzing');
            const sidebar = document.querySelector('.sidebar');
            const info = {
                exists: !!sidebar,
                left: sidebar ? sidebar.style.left : 'N/A',
                classes: sidebar ? sidebar.className : 'N/A',
                display: sidebar ? getComputedStyle(sidebar).display : 'N/A',
                visibility: sidebar ? getComputedStyle(sidebar).visibility : 'N/A',
                zIndex: sidebar ? getComputedStyle(sidebar).zIndex : 'N/A'
            };
            log('📊 Analysis: ' + JSON.stringify(info, null, 2));
        }

        function log(message) {
            const time = new Date().toLocaleTimeString();
            const entry = `${time}: ${message}`;
            logs.push(entry);
            console.log(entry);

            const logEl = document.getElementById('log');
            logEl.textContent = logs.slice(-10).join('\n');
            logEl.scrollTop = logEl.scrollHeight;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Page loaded');
            log(`Screen: ${window.innerWidth}x${window.innerHeight}`);
            analyze();
        });
    </script>
</body>
</html>
