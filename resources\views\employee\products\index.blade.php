@extends('layouts.app')

@section('title', 'Produk')

@section('content')
<div class="container">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1 class="page-title">
                    <i class="fas fa-box"></i>
                    Produk
                </h1>
                <p class="page-description">Daftar produk yang tersedia di cabang Anda</p>
            </div>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="filter-card">
        <form method="GET" action="{{ route('employee.products.index') }}">
            <div class="filter-content">
                <div class="filter-group">
                    <label for="search">Cari Produk</label>
                    <input type="text" id="search" name="search" class="form-control" 
                           placeholder="Nama, kode, atau deskripsi produk..." 
                           value="{{ request('search') }}">
                </div>
                
                <div class="filter-group">
                    <label for="category_id">Kategori</label>
                    <select id="category_id" name="category_id" class="form-control">
                        <option value="">Semua <PERSON></option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" 
                                    {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="status">Status</label>
                    <select id="status" name="status" class="form-control">
                        <option value="">Semua Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Tidak Aktif</option>
                    </select>
                </div>
                
                <div class="filter-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Cari
                    </button>
                    <a href="{{ route('employee.products.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Products Grid -->
    <div class="products-grid">
        @forelse($products as $product)
            <div class="product-card">
                <div class="product-image">
                    @if($product->image)
                        <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                    @else
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                            <span>Tidak ada gambar</span>
                        </div>
                    @endif
                </div>
                
                <div class="product-info">
                    <h3 class="product-name">{{ $product->name }}</h3>
                    <p class="product-code">{{ $product->code }}</p>
                    <p class="product-category">{{ $product->category->name ?? 'Tanpa Kategori' }}</p>
                    
                    <div class="product-price">
                        <span class="price">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="product-stock">
                        <span class="stock {{ $product->stock <= 10 ? 'low-stock' : '' }}">
                            Stok: {{ $product->stock }}
                        </span>
                    </div>
                    
                    <div class="product-status">
                        <span class="badge {{ $product->status == 'active' ? 'badge-success' : 'badge-danger' }}">
                            {{ $product->status == 'active' ? 'Aktif' : 'Tidak Aktif' }}
                        </span>
                    </div>
                    
                    <div class="product-actions">
                        <a href="{{ route('employee.products.show', $product) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                            Detail
                        </a>
                    </div>
                </div>
            </div>
        @empty
            <div class="empty-state">
                <div class="empty-content">
                    <i class="fas fa-box-open"></i>
                    <h3>Tidak ada produk</h3>
                    <p>Belum ada produk yang tersedia di cabang Anda.</p>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($products->hasPages())
        <div class="pagination-wrapper">
            {{ $products->appends(request()->query())->links() }}
        </div>
    @endif
</div>

<style>
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.product-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.product-image {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    text-align: center;
    color: #6c757d;
}

.no-image i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.product-code {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.product-category {
    font-size: 0.875rem;
    color: #3b82f6;
    margin-bottom: 1rem;
}

.product-price {
    margin-bottom: 0.75rem;
}

.price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #059669;
}

.product-stock {
    margin-bottom: 0.75rem;
}

.stock {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.stock.low-stock {
    color: #dc2626;
}

.product-status {
    margin-bottom: 1rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .product-card {
        margin-bottom: 1rem;
    }
}
</style>
@endsection
