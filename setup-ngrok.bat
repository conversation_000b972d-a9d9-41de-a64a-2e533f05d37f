@echo off
echo ========================================
echo    SETUP NGROK UNTUK MOBILE TESTING
echo ========================================
echo.

echo 1. Building production assets...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build assets
    pause
    exit /b 1
)

echo.
echo 2. Clearing Laravel cache...
call php artisan config:clear
call php artisan cache:clear
call php artisan view:clear

echo.
echo 3. Starting Laravel server...
start "Laravel Server" cmd /k "php artisan serve"

echo.
echo 4. Waiting for server to start...
timeout /t 3 /nobreak > nul

echo.
echo 5. Starting ngrok tunnel...
echo.
echo IMPORTANT: 
echo 1. Copy the HTTPS URL from ngrok (e.g., https://abc123.ngrok-free.app)
echo 2. Update APP_URL in .env file with that URL
echo 3. Run: php artisan config:clear
echo 4. Test on mobile device
echo.
echo Starting ngrok...
ngrok http 8000

pause
