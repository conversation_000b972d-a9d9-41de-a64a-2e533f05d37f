<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sidebar Simple</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f0f9ff;
            overflow-x: hidden;
        }

        /* Mobile Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            padding: 10px;
            cursor: pointer;
        }

        .mobile-title {
            font-size: 18px;
            font-weight: 600;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            color: white;
            overflow-y: auto;
        }

        .sidebar.active {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            gap: 15px;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .nav-text {
            color: white;
            font-size: 14px;
        }

        /* Overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-overlay.active {
            display: block;
            opacity: 1;
        }

        /* Main Content */
        .main-content {
            padding: 70px 20px 20px 20px;
        }

        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .debug-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">CATAT TEST</h1>
        <div style="width: 32px;"></div>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <h2>CATAT</h2>
            <p>Sistem POS</p>
        </div>
        <ul class="nav-list">
            <a href="#" class="nav-item">
                <i class="fas fa-home"></i>
                <span class="nav-text">Dashboard</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-users"></i>
                <span class="nav-text">Pegawai</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-store"></i>
                <span class="nav-text">Cabang</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-box"></i>
                <span class="nav-text">Produk</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span class="nav-text">Laporan</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Logout</span>
            </a>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="test-card">
            <h2>Test Sidebar Mobile</h2>
            <p>Klik tombol hamburger di header atau tombol debug untuk test sidebar.</p>
            <button class="test-btn" onclick="toggleSidebar()">Toggle Sidebar</button>
            <button class="test-btn" onclick="testSidebar()">Test Manual</button>
        </div>

        <div class="test-card">
            <h3>Status</h3>
            <div id="status">Ready</div>
        </div>
    </main>

    <!-- Debug Button -->
    <button class="debug-btn" onclick="debugSidebar()">🔧</button>

    <script>
        function toggleSidebar() {
            console.log('Toggle sidebar called');
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');
            
            if (!sidebar) {
                updateStatus('Error: Sidebar not found');
                return;
            }

            const isOpen = sidebar.classList.contains('active');
            
            if (isOpen) {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
                if (menuBtn) menuBtn.className = 'fas fa-bars';
                updateStatus('Sidebar closed');
            } else {
                sidebar.classList.add('active');
                overlay.classList.add('active');
                if (menuBtn) menuBtn.className = 'fas fa-times';
                updateStatus('Sidebar opened');
            }
        }

        function closeSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (sidebar) sidebar.classList.remove('active');
            if (overlay) overlay.classList.remove('active');
            if (menuBtn) menuBtn.className = 'fas fa-bars';
            updateStatus('Sidebar closed via overlay');
        }

        function testSidebar() {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.style.transform = sidebar.style.transform === 'translateX(0px)' ? 'translateX(-100%)' : 'translateX(0px)';
                updateStatus('Manual test: ' + sidebar.style.transform);
            }
        }

        function debugSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            
            console.log('Sidebar element:', sidebar);
            console.log('Sidebar classes:', sidebar ? sidebar.className : 'not found');
            console.log('Overlay element:', overlay);
            
            updateStatus(`Debug: sidebar=${!!sidebar}, overlay=${!!overlay}, classes=${sidebar ? sidebar.className : 'none'}`);
            
            if (sidebar) {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
            }
        }

        function updateStatus(message) {
            const status = document.getElementById('status');
            const time = new Date().toLocaleTimeString();
            status.innerHTML = `${time}: ${message}`;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Page loaded and ready');
        });
    </script>
</body>
</html>
