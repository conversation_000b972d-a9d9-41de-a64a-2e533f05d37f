<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CATAT">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <title>@yield('title', 'CATAT')</title>

    <!-- Flash Messages for Toast -->
    @if(session('success'))
        <meta name="flash-success" content="{{ session('success') }}">
    @endif
    @if(session('error'))
        <meta name="flash-error" content="{{ session('error') }}">
    @endif
    @if(session('warning'))
        <meta name="flash-warning" content="{{ session('warning') }}">
    @endif
    @if(session('info'))
        <meta name="flash-info" content="{{ session('info') }}">
    @endif
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    @php
        $useBuiltAssets = app()->environment('production') ||
                         str_contains(config('app.url'), 'ngrok') ||
                         str_contains(request()->getHost(), 'ngrok') ||
                         file_exists(public_path('build/manifest.json'));
    @endphp

    @if($useBuiltAssets && file_exists(public_path('build/manifest.json')))
        <!-- Production/ngrok: Use built assets -->
        @php
            $manifest = json_decode(file_get_contents(public_path('build/manifest.json')), true);
            $cssFile = $manifest['resources/css/app.css']['file'] ?? 'assets/app-DQhsI7HA.css';
            $jsFile = $manifest['resources/js/app.js']['file'] ?? 'assets/app-CQXzpyVp.js';
        @endphp
        <link rel="stylesheet" href="{{ asset('build/' . $cssFile) }}">
        <script src="{{ asset('build/' . $jsFile) }}" defer></script>
    @else
        <!-- Development: Use Vite -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @endif
    <script src="{{ asset('js/toast.js') }}"></script>
    <script src="{{ asset('js/mobile.js') }}"></script>

    <!-- Mobile Responsive CSS -->
    <style>
        /* Mobile Header Base Styles */
        .mobile-header {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100vw;
            max-width: 100vw;
            height: 50px;
            z-index: 1001;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            color: white;
        }

        .mobile-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* FORCE MOBILE HEADER VISIBLE */
        @media (max-width: 768px) {
            .mobile-header {
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100vw !important;
                height: 50px !important;
                z-index: 9997 !important;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
                color: white !important;
                align-items: center !important;
                justify-content: space-between !important;
                padding: 0 15px !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
            }

            body {
                padding-top: 50px !important;
                margin: 0 !important;
            }

            .main-content {
                padding-top: 70px !important;
                margin-left: 0 !important;
            }

            /* Ensure mobile menu button is visible */
            .mobile-menu-btn {
                display: block !important;
                background: none !important;
                border: none !important;
                color: white !important;
                font-size: 20px !important;
                padding: 10px !important;
                cursor: pointer !important;
                border-radius: 5px !important;
            }

            .mobile-menu-btn:hover {
                background: rgba(255,255,255,0.1) !important;
            }

            .mobile-title {
                font-size: 18px !important;
                font-weight: 600 !important;
                color: white !important;
                margin: 0 !important;
            }
        }
        /* ULTRA SIMPLE MOBILE SIDEBAR */
        .mobile-sidebar {
            position: fixed;
            top: 0;
            left: -100%;
            width: 280px;
            height: 100vh;
            background: #1e40af;
            z-index: 99999;
            transition: left 0.3s ease;
            overflow-y: auto;
            color: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.5);
        }

        .mobile-sidebar.show {
            left: 0;
        }

        .mobile-sidebar-header {
            padding: 30px 20px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            background: rgba(0,0,0,0.1);
        }

        .mobile-sidebar-header h2 {
            color: white;
            font-size: 24px;
            margin: 0 0 10px 0;
        }

        .mobile-nav-list {
            padding: 0;
            margin: 0;
        }

        .mobile-nav-item {
            display: block;
            padding: 20px 25px;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 16px;
            transition: background 0.2s ease;
        }

        .mobile-nav-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }

        .mobile-nav-item i {
            display: inline-block;
            width: 25px;
            margin-right: 15px;
            color: white;
            font-size: 16px;
        }

        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99998;
            display: none;
        }

        .mobile-overlay.show {
            display: block;
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }

            .main-content {
                margin-left: 0;
                padding: 70px 15px 15px 15px;
            }
        }

            .mobile-header {
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100vw !important;
                max-width: 100vw !important;
                height: 50px !important;
                z-index: 1001 !important;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
                padding: 10px 15px !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
                align-items: center !important;
                justify-content: space-between !important;
                box-sizing: border-box !important;
                color: white !important;
            }

            .mobile-title {
                font-size: 18px !important;
                font-weight: 600 !important;
                margin: 0 !important;
            }

            .mobile-menu-btn {
                background: none !important;
                border: none !important;
                color: white !important;
                font-size: 18px !important;
                padding: 8px !important;
                cursor: pointer !important;
                border-radius: 4px !important;
                transition: background-color 0.2s !important;
            }

            .mobile-menu-btn:hover {
                background: rgba(255, 255, 255, 0.1) !important;
            }

            .content-wrapper {
                margin-top: 60px !important;
                padding: 15px 10px !important;
            }

            .dashboard-cards {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
                padding: 0 !important;
            }

            .card {
                margin: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            }

            .card-content {
                padding: 15px !important;
            }

            .card-icon {
                width: 50px !important;
                height: 50px !important;
                font-size: 24px !important;
            }

            .card-info h3 {
                font-size: 18px !important;
                margin-bottom: 5px !important;
            }

            .card-info p {
                font-size: 12px !important;
            }

            .card-value {
                font-size: 20px !important;
            }

            /* Fix untuk container yang overflow */
            * {
                max-width: 100vw !important;
                box-sizing: border-box !important;
            }

            .container, .row, .col {
                width: 100% !important;
                max-width: 100% !important;
                padding-left: 10px !important;
                padding-right: 10px !important;
            }
        }

        /* Mobile Header */
        .mobile-header {
            display: none;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .mobile-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        .mobile-overlay.active {
            display: block;
        }

        /* ANDROID/MOBILE SPECIFIC FIXES */
        @media (max-width: 768px) {
            /* Mobile header visibility */
            .mobile-header {
                display: flex !important;
            }

            /* Main content adjustment */
            .main-content {
                padding-top: 60px !important;
                margin-left: 0 !important;
            }

            /* FORCE SIDEBAR CONTENT VISIBLE ON MOBILE */
            .sidebar {
                background: #1e40af !important;
                color: white !important;
            }

            .sidebar * {
                color: white !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .sidebar .nav-list {
                display: block !important;
                list-style: none !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .sidebar .nav-item {
                display: flex !important;
                align-items: center !important;
                padding: 18px 20px !important;
                color: white !important;
                text-decoration: none !important;
                border-bottom: 1px solid rgba(255,255,255,0.1) !important;
                gap: 15px !important;
                font-size: 15px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            .sidebar .nav-item:hover {
                background: rgba(255,255,255,0.1) !important;
            }

            .sidebar .nav-item i {
                font-size: 16px !important;
                width: 20px !important;
                text-align: center !important;
                color: white !important;
                display: inline-block !important;
            }

            .sidebar .nav-text {
                display: inline !important;
                color: white !important;
                font-size: 15px !important;
                font-weight: 500 !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .sidebar .sidebar-header {
                display: block !important;
                padding: 20px !important;
                border-bottom: 1px solid rgba(255,255,255,0.2) !important;
                color: white !important;
            }

            .sidebar .welcome-message {
                display: block !important;
                color: white !important;
                margin-top: 15px !important;
            }

            .sidebar .logo {
                display: block !important;
                color: white !important;
                text-decoration: none !important;
            }

            /* Mobile overlay */
            .mobile-overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, 0.5) !important;
                z-index: 1000 !important;
                display: none !important;
                opacity: 0 !important;
                transition: opacity 0.3s ease !important;
            }

            .mobile-overlay.active {
                display: block !important;
                opacity: 1 !important;
            }
        }
    </style>

    @stack('styles')
    @stack('scripts')
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">CATAT</h1>
        <div style="width: 32px;"></div> <!-- Spacer for centering -->
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Mobile Sidebar (Simple Version) -->
    <div class="mobile-sidebar">
        <div class="mobile-sidebar-header">
            <h2>CATAT</h2>
            <p style="color: rgba(255,255,255,0.8); margin: 5px 0 0 0;">Sistem POS</p>
            @if(auth()->check())
            <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 14px;">
                {{ Auth::user()->name }}
            </p>
            @endif
        </div>
        <div class="mobile-nav-list">
            @if(auth()->check() && auth()->user()->isDirector())
                <a href="{{ route('director.dashboard') }}" class="mobile-nav-item">
                    <i class="fas fa-home"></i>
                    Beranda
                </a>
                <a href="{{ route('director.branches.index') }}" class="mobile-nav-item">
                    <i class="fas fa-store"></i>
                    Manajemen Cabang
                </a>
                <a href="{{ route('director.users.index') }}" class="mobile-nav-item">
                    <i class="fas fa-users"></i>
                    Manajemen Pengguna
                </a>
                <a href="{{ route('director.products.index') }}" class="mobile-nav-item">
                    <i class="fas fa-box"></i>
                    Manajemen Produk
                </a>
                <a href="{{ route('director.reports.index') }}" class="mobile-nav-item">
                    <i class="fas fa-chart-line"></i>
                    Laporan
                </a>
            @elseif(auth()->check() && auth()->user()->isManager())
                <a href="{{ route('manager.dashboard') }}" class="mobile-nav-item">
                    <i class="fas fa-home"></i>
                    Beranda
                </a>
                <a href="{{ route('manager.employees.index') }}" class="mobile-nav-item">
                    <i class="fas fa-users"></i>
                    Manajemen Pegawai
                </a>
                <a href="{{ route('manager.products.index') }}" class="mobile-nav-item">
                    <i class="fas fa-box"></i>
                    Manajemen Produk
                </a>
                <a href="{{ route('manager.transactions.index') }}" class="mobile-nav-item">
                    <i class="fas fa-receipt"></i>
                    Transaksi
                </a>
                <a href="{{ route('manager.reports.index') }}" class="mobile-nav-item">
                    <i class="fas fa-chart-line"></i>
                    Laporan
                </a>
            @elseif(auth()->check() && auth()->user()->isEmployee())
                <a href="{{ route('employee.dashboard') }}" class="mobile-nav-item">
                    <i class="fas fa-home"></i>
                    Beranda
                </a>
                <a href="{{ route('employee.products.index') }}" class="mobile-nav-item">
                    <i class="fas fa-box"></i>
                    Produk
                </a>
                <a href="{{ route('employee.transactions.index') }}" class="mobile-nav-item">
                    <i class="fas fa-receipt"></i>
                    Transaksi
                </a>
                <a href="{{ route('employee.reports.index') }}" class="mobile-nav-item">
                    <i class="fas fa-chart-line"></i>
                    Laporan
                </a>
            @endif
            <a href="{{ route('logout') }}" class="mobile-nav-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                <i class="fas fa-sign-out-alt"></i>
                Keluar
            </a>
        </div>
    </div>

    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="{{ route('login') }}" class="logo">
                <img src="{{ asset('logo.png') }}" alt="CATAT Logo" style="height: 160px; width: auto;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none; color: white; font-size: 24px; font-weight: 700; text-align: center; padding: 20px;">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                    CATAT
                </div>
            </a>

            <!-- Welcome Message -->
            @if(auth()->check())
            <div class="welcome-message">
                <div style="color: white; font-size: 13px; font-weight: 600; text-shadow: 0 1px 2px rgb(171, 161, 161); line-height: 1.4; text-align: center;">
                    Selamat Datang<br>
                    <span style="font-size: 15px; font-weight: 700;">{{ Auth::user()->name }}</span><br>
                    <span style="font-size: 11px; opacity: 0.9;">
                        Di Website
                        @if(Auth::user()->role === 'director')
                            Direktur
                        @elseif(Auth::user()->role === 'manager')
                            Manajer
                        @elseif(Auth::user()->role === 'employee')
                            Pegawai
                        @else
                            {{ ucfirst(Auth::user()->role) }}
                        @endif
                    </span>
                </div>
            </div>
            @endif
        </div>

        <ul class="nav-list">
            @if(auth()->check() && auth()->user()->isDirector())
                <a href="{{ route('director.dashboard') }}" class="nav-item {{ request()->routeIs('director.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="{{ route('director.branches.index') }}" class="nav-item {{ request()->routeIs('director.branches.*') ? 'active' : '' }}">
                    <i class="fas fa-store"></i>
                    <span class="nav-text">Manajemen Cabang</span>
                </a>
                <a href="{{ route('director.users.index') }}" class="nav-item {{ request()->routeIs('director.users.*') ? 'active' : '' }}">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="{{ route('director.reports.integrated') }}" class="nav-item {{ request()->routeIs('director.reports.integrated') ? 'active' : '' }}">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            @elseif(auth()->check() && auth()->user()->isManager())
                <a href="{{ route('manager.dashboard') }}" class="nav-item {{ request()->routeIs('manager.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="{{ route('manager.products.index') }}" class="nav-item {{ request()->routeIs('manager.products.*') ? 'active' : '' }}">
                    <i class="fas fa-box"></i>
                    <span class="nav-text">Manajemen Produk</span>
                </a>
                <a href="{{ route('manager.categories.index') }}" class="nav-item {{ request()->routeIs('manager.categories.*') ? 'active' : '' }}">
                    <i class="fas fa-tags"></i>
                    <span class="nav-text">Manajemen Kategori</span>
                </a>
                <a href="{{ route('manager.employees.index') }}" class="nav-item {{ request()->routeIs('manager.employees.*') ? 'active' : '' }}">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="{{ route('manager.returns.index') }}" class="nav-item {{ request()->routeIs('manager.returns.*') ? 'active' : '' }}">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Persetujuan Retur</span>
                </a>
                <a href="{{ route('manager.damaged-stock.index') }}" class="nav-item {{ request()->routeIs('manager.damaged-stock.*') ? 'active' : '' }}">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="nav-text">Barang Rusak</span>
                </a>
                <a href="{{ route('manager.reports.integrated') }}" class="nav-item {{ request()->routeIs('manager.reports.integrated') ? 'active' : '' }}">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            @elseif(auth()->check() && auth()->user()->isEmployee())
                <a href="{{ route('employee.dashboard') }}" class="nav-item {{ request()->routeIs('employee.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="{{ route('employee.transactions.create') }}" class="nav-item {{ request()->routeIs('employee.transactions.*') ? 'active' : '' }}">
                    <i class="fas fa-cash-register"></i>
                    <span class="nav-text">Transaksi Penjualan</span>
                </a>
                <a href="{{ route('employee.returns.index') }}" class="nav-item {{ request()->routeIs('employee.returns.*') ? 'active' : '' }}">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Retur Barang</span>
                </a>
                <a href="{{ route('employee.stocks.index') }}" class="nav-item {{ request()->routeIs('employee.stocks.*') ? 'active' : '' }}">
                    <i class="fas fa-boxes"></i>
                    <span class="nav-text">Stok Produk</span>
                </a>
            @endif

            <a href="{{ route('logout') }}" class="nav-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Keluar</span>
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                @csrf
            </form>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-wrapper">
            {{-- Top Bar - Moved to individual views --}}
            {{-- Page-specific Content --}}
            @yield('content')
        </div>
    </main>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    {{-- Include global scripts or page-specific scripts here if needed --}}
    <script>
        // ULTRA SIMPLE TOGGLE
        function toggleMobileMenu() {
            console.log('🔄 Toggle mobile menu');

            const sidebar = document.querySelector('.mobile-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const btn = document.querySelector('.mobile-menu-btn i');

            console.log('Elements found:', {
                sidebar: !!sidebar,
                overlay: !!overlay,
                btn: !!btn
            });

            if (!sidebar) {
                console.error('❌ Sidebar not found');
                alert('Sidebar element not found!');
                return;
            }

            // Simple toggle
            if (sidebar.style.left === '0px' || sidebar.classList.contains('show')) {
                console.log('🔒 Closing');
                sidebar.style.left = '-100%';
                sidebar.classList.remove('show');
                if (overlay) overlay.style.display = 'none';
                if (btn) btn.className = 'fas fa-bars';
            } else {
                console.log('🔓 Opening');
                sidebar.style.left = '0px';
                sidebar.classList.add('show');
                if (overlay) overlay.style.display = 'block';
                if (btn) btn.className = 'fas fa-times';
            }
        }

        function closeMobileMenu() {
            console.log('🔒 Close menu');
            const sidebar = document.querySelector('.mobile-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const btn = document.querySelector('.mobile-menu-btn i');

            if (sidebar) {
                sidebar.style.left = '-100%';
                sidebar.classList.remove('show');
            }
            if (overlay) overlay.style.display = 'none';
            if (btn) btn.className = 'fas fa-bars';
        }

        // Simple initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 Simple mobile menu initialized');

            // Prevent horizontal scroll
            document.body.style.overflowX = 'hidden';
            document.documentElement.style.overflowX = 'hidden';

            // Close menu when clicking mobile nav items
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', closeMobileMenu);
            });

            // Close menu when clicking overlay
            const overlay = document.querySelector('.mobile-overlay');
            if (overlay) {
                overlay.addEventListener('click', closeMobileMenu);
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    closeMobileMenu();
                }
            });

            // Add multiple debug buttons
            const debugContainer = document.createElement('div');
            debugContainer.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 999999;
                display: flex;
                flex-direction: column;
                gap: 10px;
            `;

            // Main toggle button
            const toggleBtn = document.createElement('button');
            toggleBtn.innerHTML = '☰';
            toggleBtn.style.cssText = `
                background: #ff4444;
                color: white;
                border: none;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                font-size: 24px;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            `;
            toggleBtn.onclick = function() {
                console.log('🔧 Manual toggle');
                toggleMobileMenu();
            };

            // Force show button
            const forceBtn = document.createElement('button');
            forceBtn.innerHTML = '👁️';
            forceBtn.style.cssText = `
                background: #00aa00;
                color: white;
                border: none;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                font-size: 18px;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            `;
            forceBtn.onclick = function() {
                console.log('👁️ Force show');
                const sidebar = document.querySelector('.mobile-sidebar');
                if (sidebar) {
                    sidebar.style.left = '0px';
                    sidebar.style.display = 'block';
                    sidebar.style.visibility = 'visible';
                    sidebar.style.opacity = '1';
                    sidebar.classList.add('show');
                    console.log('Sidebar forced visible');
                } else {
                    alert('Sidebar element not found!');
                }
            };

            // Info button
            const infoBtn = document.createElement('button');
            infoBtn.innerHTML = 'ℹ️';
            infoBtn.style.cssText = `
                background: #0066cc;
                color: white;
                border: none;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                font-size: 18px;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            `;
            infoBtn.onclick = function() {
                const sidebar = document.querySelector('.mobile-sidebar');
                const info = {
                    sidebarExists: !!sidebar,
                    sidebarStyle: sidebar ? sidebar.style.cssText : 'not found',
                    sidebarClasses: sidebar ? sidebar.className : 'not found',
                    screenWidth: window.innerWidth,
                    userAgent: navigator.userAgent.substring(0, 50)
                };
                console.log('📱 Debug info:', info);
                alert('Check console for debug info');
            };

            debugContainer.appendChild(toggleBtn);
            debugContainer.appendChild(forceBtn);
            debugContainer.appendChild(infoBtn);
            document.body.appendChild(debugContainer);
        });

            // Show flash messages as toasts
            @if(session('success'))
                showToast('{{ session('success') }}', 'success');
            @endif

            @if(session('error'))
                showToast('{{ session('error') }}', 'error');
            @endif

            @if(session('warning'))
                showToast('{{ session('warning') }}', 'warning');
            @endif

            @if(session('info'))
                showToast('{{ session('info') }}', 'info');
            @endif
        });

        // Disable mobile.js if mobile header exists
        window.mobileHeaderExists = true;
    </script>
    <script>
        // Auto-refresh CSRF token every 30 minutes to prevent expiration
        setInterval(function() {
            fetch('/csrf-token')
                .then(response => response.json())
                .then(data => {
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.token);
                    }
                    // Update all CSRF token inputs
                    document.querySelectorAll('input[name="_token"]').forEach(input => {
                        input.value = data.token;
                    });
                })
                .catch(error => {
                    console.log('CSRF token refresh failed:', error);
                });
        }, 30 * 60 * 1000); // 30 minutes

        // Handle logout form submission with better error handling
        document.addEventListener('DOMContentLoaded', function() {
            const logoutForm = document.getElementById('logout-form');
            if (logoutForm) {
                logoutForm.addEventListener('submit', function(e) {
                    // If CSRF token is missing or expired, redirect to login
                    const csrfTokenInput = logoutForm.querySelector('input[name="_token"]');
                    if (!csrfTokenInput || !csrfTokenInput.value) {
                        e.preventDefault();
                        window.location.href = '{{ route("login") }}';
                        return false;
                    }
                });
            }

            // Handle logout link click with fallback
            const logoutLink = document.querySelector('a[href="{{ route("logout") }}"]');
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = document.getElementById('logout-form');
                    if (form) {
                        form.submit();
                    } else {
                        // Fallback: redirect to logout GET route
                        window.location.href = '{{ route("logout.get") }}';
                    }
                });
            }
        });
    </script>
</body>
</html>
