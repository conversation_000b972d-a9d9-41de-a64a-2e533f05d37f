<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CATAT">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <title>@yield('title', 'CATAT')</title>

    <!-- Flash Messages for Toast -->
    @if(session('success'))
        <meta name="flash-success" content="{{ session('success') }}">
    @endif
    @if(session('error'))
        <meta name="flash-error" content="{{ session('error') }}">
    @endif
    @if(session('warning'))
        <meta name="flash-warning" content="{{ session('warning') }}">
    @endif
    @if(session('info'))
        <meta name="flash-info" content="{{ session('info') }}">
    @endif
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    @php
        $useBuiltAssets = app()->environment('production') ||
                         str_contains(config('app.url'), 'ngrok') ||
                         str_contains(request()->getHost(), 'ngrok') ||
                         file_exists(public_path('build/manifest.json'));
    @endphp

    @if($useBuiltAssets && file_exists(public_path('build/manifest.json')))
        <!-- Production/ngrok: Use built assets -->
        @php
            $manifest = json_decode(file_get_contents(public_path('build/manifest.json')), true);
            $cssFile = $manifest['resources/css/app.css']['file'] ?? 'assets/app-DQhsI7HA.css';
            $jsFile = $manifest['resources/js/app.js']['file'] ?? 'assets/app-CQXzpyVp.js';
        @endphp
        <link rel="stylesheet" href="{{ asset('build/' . $cssFile) }}">
        <script src="{{ asset('build/' . $jsFile) }}" defer></script>
    @else
        <!-- Development: Use Vite -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @endif
    <script src="{{ asset('js/toast.js') }}"></script>
    <script src="{{ asset('js/mobile.js') }}"></script>

    <!-- Mobile Responsive CSS -->
    <style>
        /* Mobile Header Base Styles */
        .mobile-header {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100vw;
            max-width: 100vw;
            height: 50px;
            z-index: 1001;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            color: white;
        }

        .mobile-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Force mobile header to show on mobile */
        @media (max-width: 768px) {
            .mobile-header {
                display: flex !important;
            }

            body {
                padding-top: 50px !important;
            }

            .main-content {
                padding-top: 70px !important;
            }
        }
        /* Mobile Layout Fixes */
        @media (max-width: 768px) {
            html, body {
                overflow-x: hidden !important;
                width: 100vw !important;
                max-width: 100vw !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .main-content {
                width: 100% !important;
                max-width: 100vw !important;
                margin-left: 0 !important;
                padding: 60px 10px 10px 10px !important;
                box-sizing: border-box !important;
                overflow-x: hidden !important;
            }

            .sidebar {
                transform: translateX(-100%) !important;
                z-index: 1000 !important;
                width: 280px !important;
                position: fixed !important;
            }

            .sidebar.active, .sidebar.open {
                transform: translateX(0) !important;
            }

            /* Ensure all content respects viewport */
            * {
                max-width: 100vw !important;
                box-sizing: border-box !important;
            }

            .mobile-header {
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100vw !important;
                max-width: 100vw !important;
                height: 50px !important;
                z-index: 1001 !important;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
                padding: 10px 15px !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
                align-items: center !important;
                justify-content: space-between !important;
                box-sizing: border-box !important;
                color: white !important;
            }

            .mobile-title {
                font-size: 18px !important;
                font-weight: 600 !important;
                margin: 0 !important;
            }

            .mobile-menu-btn {
                background: none !important;
                border: none !important;
                color: white !important;
                font-size: 18px !important;
                padding: 8px !important;
                cursor: pointer !important;
                border-radius: 4px !important;
                transition: background-color 0.2s !important;
            }

            .mobile-menu-btn:hover {
                background: rgba(255, 255, 255, 0.1) !important;
            }

            .content-wrapper {
                margin-top: 60px !important;
                padding: 15px 10px !important;
            }

            .dashboard-cards {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
                padding: 0 !important;
            }

            .card {
                margin: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            }

            .card-content {
                padding: 15px !important;
            }

            .card-icon {
                width: 50px !important;
                height: 50px !important;
                font-size: 24px !important;
            }

            .card-info h3 {
                font-size: 18px !important;
                margin-bottom: 5px !important;
            }

            .card-info p {
                font-size: 12px !important;
            }

            .card-value {
                font-size: 20px !important;
            }

            /* Fix untuk container yang overflow */
            * {
                max-width: 100vw !important;
                box-sizing: border-box !important;
            }

            .container, .row, .col {
                width: 100% !important;
                max-width: 100% !important;
                padding-left: 10px !important;
                padding-right: 10px !important;
            }
        }

        /* Mobile Header */
        .mobile-header {
            display: none;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .mobile-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        .mobile-overlay.active {
            display: block;
        }

        /* Simple Mobile Sidebar - Clean Approach */
        @media (max-width: 768px) {
            /* Reset all sidebar styles for mobile */
            .sidebar {
                position: fixed !important;
                top: 0 !important;
                left: -300px !important; /* Start hidden */
                width: 300px !important;
                height: 100vh !important;
                background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
                z-index: 1002 !important;
                transition: left 0.3s ease !important;
                overflow-y: auto !important;
                box-shadow: 2px 0 10px rgba(0,0,0,0.3) !important;
                transform: none !important; /* Remove transform conflicts */
            }

            /* Show sidebar when active */
            .sidebar.mobile-open {
                left: 0 !important;
            }

            /* Mobile overlay */
            .mobile-overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, 0.5) !important;
                z-index: 1001 !important;
                display: none !important;
            }

            .mobile-overlay.show {
                display: block !important;
            }

            /* Ensure sidebar content is visible on mobile */
            .sidebar .nav-list {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .sidebar .nav-item {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                padding: 15px 20px !important;
                color: white !important;
                text-decoration: none !important;
                align-items: center !important;
                gap: 12px !important;
                border-bottom: 1px solid rgba(255,255,255,0.1) !important;
                transition: background-color 0.2s ease !important;
            }

            .sidebar .nav-item:hover {
                background-color: rgba(255,255,255,0.1) !important;
            }

            .sidebar .nav-item.active {
                background-color: rgba(255,255,255,0.2) !important;
            }

            .sidebar .nav-item i {
                font-size: 16px !important;
                width: 20px !important;
                text-align: center !important;
                color: white !important;
            }

            .sidebar .nav-text {
                display: inline !important;
                visibility: visible !important;
                opacity: 1 !important;
                color: white !important;
                font-size: 14px !important;
                font-weight: 500 !important;
            }

            .sidebar .sidebar-header {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                padding: 20px !important;
                border-bottom: 1px solid rgba(255,255,255,0.2) !important;
            }

            .sidebar .welcome-message {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                margin-top: 15px !important;
            }

            .sidebar .logo {
                display: block !important;
                text-align: center !important;
            }

            /* Debug borders to see elements */
            .sidebar {
                border: 3px solid lime !important; /* Green border for sidebar */
            }

            .sidebar .nav-list {
                border: 2px solid yellow !important; /* Yellow border for nav-list */
            }

            .sidebar .nav-item {
                border: 1px solid red !important; /* Red border for nav-items */
                margin: 2px 0 !important;
            }

            .sidebar .nav-text {
                background: rgba(255,255,0,0.3) !important; /* Yellow background for text */
            }
        }
    </style>

    @stack('styles')
    @stack('scripts')
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="mobile-title">CATAT</h1>
        <div style="width: 32px;"></div> <!-- Spacer for centering -->
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="{{ route('login') }}" class="logo">
                <img src="{{ asset('logo.png') }}" alt="CATAT Logo" style="height: 160px; width: auto;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none; color: white; font-size: 24px; font-weight: 700; text-align: center; padding: 20px;">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                    CATAT
                </div>
            </a>

            <!-- Welcome Message -->
            @if(auth()->check())
            <div class="welcome-message">
                <div style="color: white; font-size: 13px; font-weight: 600; text-shadow: 0 1px 2px rgb(171, 161, 161); line-height: 1.4; text-align: center;">
                    Selamat Datang<br>
                    <span style="font-size: 15px; font-weight: 700;">{{ Auth::user()->name }}</span><br>
                    <span style="font-size: 11px; opacity: 0.9;">
                        Di Website
                        @if(Auth::user()->role === 'director')
                            Direktur
                        @elseif(Auth::user()->role === 'manager')
                            Manajer
                        @elseif(Auth::user()->role === 'employee')
                            Pegawai
                        @else
                            {{ ucfirst(Auth::user()->role) }}
                        @endif
                    </span>
                </div>
            </div>
            @endif
        </div>

        <ul class="nav-list">
            @if(auth()->check() && auth()->user()->isDirector())
                <a href="{{ route('director.dashboard') }}" class="nav-item {{ request()->routeIs('director.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="{{ route('director.branches.index') }}" class="nav-item {{ request()->routeIs('director.branches.*') ? 'active' : '' }}">
                    <i class="fas fa-store"></i>
                    <span class="nav-text">Manajemen Cabang</span>
                </a>
                <a href="{{ route('director.users.index') }}" class="nav-item {{ request()->routeIs('director.users.*') ? 'active' : '' }}">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="{{ route('director.reports.integrated') }}" class="nav-item {{ request()->routeIs('director.reports.integrated') ? 'active' : '' }}">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            @elseif(auth()->check() && auth()->user()->isManager())
                <a href="{{ route('manager.dashboard') }}" class="nav-item {{ request()->routeIs('manager.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="{{ route('manager.products.index') }}" class="nav-item {{ request()->routeIs('manager.products.*') ? 'active' : '' }}">
                    <i class="fas fa-box"></i>
                    <span class="nav-text">Manajemen Produk</span>
                </a>
                <a href="{{ route('manager.categories.index') }}" class="nav-item {{ request()->routeIs('manager.categories.*') ? 'active' : '' }}">
                    <i class="fas fa-tags"></i>
                    <span class="nav-text">Manajemen Kategori</span>
                </a>
                <a href="{{ route('manager.employees.index') }}" class="nav-item {{ request()->routeIs('manager.employees.*') ? 'active' : '' }}">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="{{ route('manager.returns.index') }}" class="nav-item {{ request()->routeIs('manager.returns.*') ? 'active' : '' }}">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Persetujuan Retur</span>
                </a>
                <a href="{{ route('manager.damaged-stock.index') }}" class="nav-item {{ request()->routeIs('manager.damaged-stock.*') ? 'active' : '' }}">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="nav-text">Barang Rusak</span>
                </a>
                <a href="{{ route('manager.reports.integrated') }}" class="nav-item {{ request()->routeIs('manager.reports.integrated') ? 'active' : '' }}">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            @elseif(auth()->check() && auth()->user()->isEmployee())
                <a href="{{ route('employee.dashboard') }}" class="nav-item {{ request()->routeIs('employee.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="{{ route('employee.transactions.create') }}" class="nav-item {{ request()->routeIs('employee.transactions.*') ? 'active' : '' }}">
                    <i class="fas fa-cash-register"></i>
                    <span class="nav-text">Transaksi Penjualan</span>
                </a>
                <a href="{{ route('employee.returns.index') }}" class="nav-item {{ request()->routeIs('employee.returns.*') ? 'active' : '' }}">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Retur Barang</span>
                </a>
                <a href="{{ route('employee.stocks.index') }}" class="nav-item {{ request()->routeIs('employee.stocks.*') ? 'active' : '' }}">
                    <i class="fas fa-boxes"></i>
                    <span class="nav-text">Stok Produk</span>
                </a>
            @endif

            <a href="{{ route('logout') }}" class="nav-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Keluar</span>
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                @csrf
            </form>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-wrapper">
            {{-- Top Bar - Moved to individual views --}}
            {{-- Page-specific Content --}}
            @yield('content')
        </div>
    </main>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    {{-- Include global scripts or page-specific scripts here if needed --}}
    <script>
        // Simple Mobile Menu Functions
        function toggleMobileMenu() {
            console.log('🔄 Toggle mobile menu called');

            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (!sidebar || !overlay) {
                console.error('❌ Elements not found:', { sidebar: !!sidebar, overlay: !!overlay });
                return;
            }

            const isOpen = sidebar.classList.contains('mobile-open');

            if (isOpen) {
                console.log('🔒 Closing sidebar');
                sidebar.classList.remove('mobile-open');
                overlay.classList.remove('show');
                if (menuBtn) menuBtn.className = 'fas fa-bars';
                document.body.style.overflow = ''; // Restore scroll
            } else {
                console.log('🔓 Opening sidebar');
                sidebar.classList.add('mobile-open');
                overlay.classList.add('show');
                if (menuBtn) menuBtn.className = 'fas fa-times';
                document.body.style.overflow = 'hidden'; // Prevent background scroll
            }
        }

        function closeMobileMenu() {
            console.log('🔒 Force closing mobile menu');

            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            if (sidebar) sidebar.classList.remove('mobile-open');
            if (overlay) overlay.classList.remove('show');
            if (menuBtn) menuBtn.className = 'fas fa-bars';
            document.body.style.overflow = '';
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, initializing mobile menu');

            // Wait a bit for all elements to be ready
            setTimeout(function() {
                const sidebar = document.querySelector('.sidebar');
                const mobileHeader = document.querySelector('.mobile-header');
                const menuBtn = document.querySelector('.mobile-menu-btn');
                const overlay = document.querySelector('.mobile-overlay');

                console.log('📋 Elements check:', {
                    sidebar: !!sidebar,
                    mobileHeader: !!mobileHeader,
                    menuBtn: !!menuBtn,
                    overlay: !!overlay
                });

                // Prevent horizontal scroll
                document.body.style.overflowX = 'hidden';
                document.documentElement.style.overflowX = 'hidden';

                // Close menu when clicking nav items
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.addEventListener('click', closeMobileMenu);
                });

                // Close menu when clicking overlay
                if (overlay) {
                    overlay.addEventListener('click', closeMobileMenu);
                }

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        closeMobileMenu();
                    }
                });

                // Add test button for debugging
                const testBtn = document.createElement('button');
                testBtn.innerHTML = '🔧 TEST';
                testBtn.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 9999;
                    background: #ff4444;
                    color: white;
                    padding: 10px 15px;
                    border: none;
                    border-radius: 25px;
                    font-weight: bold;
                    cursor: pointer;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                `;
                testBtn.onclick = function() {
                    console.log('🔧 Test button clicked');
                    toggleMobileMenu();

                    // Debug sidebar content
                    setTimeout(function() {
                        const navItems = document.querySelectorAll('.sidebar .nav-item');
                        const navTexts = document.querySelectorAll('.sidebar .nav-text');

                        console.log('📋 Sidebar debug:', {
                            navItems: navItems.length,
                            navTexts: navTexts.length,
                            firstNavItem: navItems[0] ? navItems[0].innerHTML : 'none',
                            firstNavText: navTexts[0] ? navTexts[0].innerHTML : 'none'
                        });

                        // Force show nav texts
                        navTexts.forEach(function(text, index) {
                            text.style.display = 'inline';
                            text.style.visibility = 'visible';
                            text.style.opacity = '1';
                            text.style.color = 'white';
                            console.log(`Nav text ${index}:`, text.innerHTML);
                        });
                    }, 500);
                };
                document.body.appendChild(testBtn);

                // Add force show button
                const forceBtn = document.createElement('button');
                forceBtn.innerHTML = '👁️ SHOW';
                forceBtn.style.cssText = `
                    position: fixed;
                    bottom: 80px;
                    right: 20px;
                    z-index: 9999;
                    background: #00ff00;
                    color: black;
                    padding: 10px 15px;
                    border: none;
                    border-radius: 25px;
                    font-weight: bold;
                    cursor: pointer;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                `;
                forceBtn.onclick = function() {
                    console.log('👁️ Force showing sidebar elements');

                    // Force show sidebar
                    const sidebar = document.querySelector('.sidebar');
                    if (sidebar) {
                        sidebar.style.left = '0px';
                        sidebar.style.display = 'block';
                        sidebar.style.visibility = 'visible';
                        sidebar.style.opacity = '1';
                    }

                    // Force show all nav elements
                    const allNavElements = document.querySelectorAll('.sidebar *');
                    allNavElements.forEach(function(el) {
                        el.style.display = el.tagName === 'SPAN' ? 'inline' : 'block';
                        el.style.visibility = 'visible';
                        el.style.opacity = '1';
                        if (el.classList.contains('nav-text')) {
                            el.style.color = 'white';
                            el.style.fontSize = '14px';
                        }
                    });

                    console.log('👁️ All elements forced visible');
                };
                document.body.appendChild(forceBtn);

                console.log('✅ Mobile menu initialization complete');
            }, 100);
        });

            // Show flash messages as toasts
            @if(session('success'))
                showToast('{{ session('success') }}', 'success');
            @endif

            @if(session('error'))
                showToast('{{ session('error') }}', 'error');
            @endif

            @if(session('warning'))
                showToast('{{ session('warning') }}', 'warning');
            @endif

            @if(session('info'))
                showToast('{{ session('info') }}', 'info');
            @endif
        });

        // Disable mobile.js if mobile header exists
        window.mobileHeaderExists = true;
    </script>
    <script>
        // Auto-refresh CSRF token every 30 minutes to prevent expiration
        setInterval(function() {
            fetch('/csrf-token')
                .then(response => response.json())
                .then(data => {
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.token);
                    }
                    // Update all CSRF token inputs
                    document.querySelectorAll('input[name="_token"]').forEach(input => {
                        input.value = data.token;
                    });
                })
                .catch(error => {
                    console.log('CSRF token refresh failed:', error);
                });
        }, 30 * 60 * 1000); // 30 minutes

        // Handle logout form submission with better error handling
        document.addEventListener('DOMContentLoaded', function() {
            const logoutForm = document.getElementById('logout-form');
            if (logoutForm) {
                logoutForm.addEventListener('submit', function(e) {
                    // If CSRF token is missing or expired, redirect to login
                    const csrfTokenInput = logoutForm.querySelector('input[name="_token"]');
                    if (!csrfTokenInput || !csrfTokenInput.value) {
                        e.preventDefault();
                        window.location.href = '{{ route("login") }}';
                        return false;
                    }
                });
            }

            // Handle logout link click with fallback
            const logoutLink = document.querySelector('a[href="{{ route("logout") }}"]');
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = document.getElementById('logout-form');
                    if (form) {
                        form.submit();
                    } else {
                        // Fallback: redirect to logout GET route
                        window.location.href = '{{ route("logout.get") }}';
                    }
                });
            }
        });
    </script>
</body>
</html>
