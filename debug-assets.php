<?php

// Debug script untuk memeriksa asset URLs
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== DEBUG ASSET URLS ===\n";
echo "APP_URL: " . config('app.url') . "\n";
echo "APP_ENV: " . config('app.env') . "\n";
echo "\n";

echo "=== ASSET PATHS ===\n";
echo "asset('css/app.css'): " . asset('css/app.css') . "\n";
echo "asset('build/assets/app-DQhsI7HA.css'): " . asset('build/assets/app-DQhsI7HA.css') . "\n";
echo "\n";

echo "=== FILE EXISTENCE ===\n";
$buildPath = public_path('build/assets/app-DQhsI7HA.css');
echo "Build CSS exists: " . (file_exists($buildPath) ? 'YES' : 'NO') . "\n";
echo "Build CSS path: " . $buildPath . "\n";
echo "\n";

echo "=== MANIFEST CHECK ===\n";
$manifestPath = public_path('build/manifest.json');
if (file_exists($manifestPath)) {
    echo "Manifest exists: YES\n";
    $manifest = json_decode(file_get_contents($manifestPath), true);
    echo "Manifest content:\n";
    print_r($manifest);
} else {
    echo "Manifest exists: NO\n";
}
echo "\n";

echo "=== RECOMMENDATIONS ===\n";
if (!str_contains(config('app.url'), 'ngrok')) {
    echo "⚠️  APP_URL tidak menggunakan ngrok URL\n";
    echo "   Update APP_URL di .env dengan URL ngrok yang sebenarnya\n";
}

if (!file_exists($buildPath)) {
    echo "⚠️  Build assets tidak ditemukan\n";
    echo "   Jalankan: npm run build\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "1. Jalankan ngrok: ngrok http 8000\n";
echo "2. Copy URL HTTPS dari ngrok\n";
echo "3. Update APP_URL di .env\n";
echo "4. Jalankan: php artisan config:clear\n";
echo "5. Test di mobile\n";
